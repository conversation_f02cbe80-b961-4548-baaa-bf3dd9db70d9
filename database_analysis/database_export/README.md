# Bon Voyage Database Structure Analysis

## Overview
This directory contains a comprehensive mapping and export of all data types and representative samples from the Bon Voyage database. The database contains **71 tables** with a total of **51,484+ records** across various data types.

## Files Generated

### Folder Structure
```
database_export/
├── README.md                                    # This documentation
├── core_analysis/
│   ├── database_structure_mapping.csv          # Complete column analysis
│   └── database_summary.csv                    # Table statistics
└── sample_data/
    └── [table_name]_sample.csv                 # Sample data files (71 files)
```

### Core Analysis Files
- **`core_analysis/database_structure_mapping.csv`** - Complete column-by-column analysis of all tables including:
  - Data types, nullable status, default values
  - Sample values from each column
  - Min/max values for numeric/date fields
  - Unique value counts

- **`core_analysis/database_summary.csv`** - High-level table statistics including:
  - Row counts and column counts per table
  - Table descriptions and purposes
  - Sorted by row count (largest first)

### Sample Data Files
- **`sample_data/[table_name]_sample.csv`** - Representative sample data (up to 100 rows) from each table

## Database Architecture

### Core Application Tables (Non-WordPress)
The main application uses **47 core tables** containing travel/booking data:

#### Primary Entity Tables
1. **`quote_requests`** (51,484 rows) - Customer quote and booking requests
2. **`images`** (16,203 rows) - Image assets and media files  
3. **`contacts`** (8,402 rows) - Customer and contact information
4. **`content_blocks`** (6,422 rows) - CMS content blocks and text
5. **`destinations`** (481 rows) - Travel destinations and locations
6. **`accommodation`** (866 rows) - Hotels, resorts and lodging properties
7. **`activities`** (936 rows) - Tours, excursions and activities offered
8. **`itineraries`** (418 rows) - Travel itineraries and trip packages
9. **`holiday_types`** (25 rows) - Categories of holiday/vacation types

#### Relationship/Junction Tables
Many tables handle many-to-many relationships:
- `destinations_images`, `destinations_itineraries`, `destinations_landing_pages`
- `accommodation_destinations`, `accommodation_images`, `accommodation_holiday_types`
- `activities_destinations`, `activities_holiday_types`
- `landing_pages_accommodations`, `landing_pages_activities`, `landing_pages_images`

#### Supporting Tables
- **`testimonials`** (110 rows) - Customer reviews and testimonials
- **`landing_pages`** (58 rows) - Marketing landing pages
- **`pages`** (52 rows) - Static pages and content
- **`faqs`** (26 rows) - Frequently asked questions
- **`users`** (7 rows) - System users and administrators

### WordPress Tables (24 tables)
Standard WordPress installation with Yoast SEO plugin:
- **`wp_posts`** (1,242 rows) - Blog posts and pages
- **`wp_postmeta`** (3,909 rows) - Post metadata
- **`wp_comments`** (752 rows) - Blog comments
- **`wp_options`** (319 rows) - WordPress configuration
- **`wp_yoast_*`** tables - SEO plugin data

## Key Data Types and Patterns

### Common Column Patterns
- **IDs**: `int(10) unsigned` auto-increment primary keys
- **Names/Titles**: `varchar(255)` for short text
- **Content**: `text` for longer content blocks
- **Slugs**: `varchar(255)` for URL-friendly identifiers
- **Timestamps**: `datetime` for created/modified tracking
- **Flags**: `tinyint(1)` for boolean values (published, featured, etc.)
- **Coordinates**: `double(11,6)` for latitude/longitude
- **Ordering**: `int(10) unsigned` for display order

### Hierarchical Data
Several tables use nested set model or parent/child relationships:
- **`destinations`** - Uses `parent_id`, `lft`, `rght` for tree structure
- **`acos`** - Access Control Objects with parent hierarchy
- **`image_folders`** - Folder hierarchy for media organization

### Meta Fields
Many entities support flexible metadata:
- `meta_title`, `meta_description`, `meta_keywords` for SEO
- Custom fields in various `*_meta` tables

## Data Volume Analysis

### Largest Tables by Row Count
1. `quote_requests` - 51,484 rows (Customer inquiries/bookings)
2. `images` - 16,203 rows (Media assets)
3. `contacts` - 8,402 rows (Customer database)
4. `content_blocks` - 6,422 rows (CMS content)
5. `itinerary_days` - 4,201 rows (Daily itinerary details)

### Most Complex Tables by Column Count
1. `wp_yoast_indexable` - 53 columns (SEO data)
2. `quote_requests` - 32 columns (Booking requests)
3. `destinations` - 26 columns (Location data)
4. `wp_posts` - 23 columns (WordPress posts)
5. `accommodation` - 21 columns (Hotel data)

## Usage Notes

### For Data Analysis
- Use `database_structure_mapping.csv` to understand column types and constraints
- Sample data files provide real examples of data formats and content
- Min/max values help understand data ranges and validation rules

### For Development
- Relationship tables follow consistent naming: `table1_table2` format
- Most entities have `created` and `modified` timestamp tracking
- Published/unpublished content uses `published` tinyint(1) flag
- Ordering is handled via `order` integer columns

### For Migration/Integration
- Primary keys are consistently `id` auto-increment integers
- Foreign keys follow `[table]_id` naming convention
- Text content may contain HTML markup
- Image references use integer IDs linking to `images` table

## Data Quality Observations
- High data completeness in core tables
- Consistent use of slugs for URL generation
- Comprehensive metadata for SEO optimization
- Well-structured relationship mappings
- Active content management with recent modification dates

This analysis provides a complete foundation for understanding the Bon Voyage database structure and planning any data migration, integration, or analysis work.
