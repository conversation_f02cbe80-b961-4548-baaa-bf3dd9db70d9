<?php
/**
 * Database Mapper and CSV Exporter
 * Maps all database tables, their structures, and exports representative data to CSV
 */

// Database configuration
$host = 'db';
$username = 'db';
$password = 'db';
$database = 'db';

// Output directory for CSV files
$outputDir = 'database_export';
if (!is_dir($outputDir)) {
    mkdir($outputDir, 0755, true);
}

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Get all tables
    $tablesQuery = $pdo->query("SHOW TABLES");
    $tables = $tablesQuery->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Found " . count($tables) . " tables.\n\n";
    
    // Create master mapping file
    $mappingFile = fopen($outputDir . '/database_structure_mapping.csv', 'w');
    fputcsv($mappingFile, [
        'Table Name',
        'Column Name', 
        'Data Type',
        'Is Nullable',
        'Default Value',
        'Extra Info',
        'Sample Values (first 3)',
        'Total Rows',
        'Unique Values',
        'Min Value',
        'Max Value'
    ]);
    
    $tableStats = [];
    
    foreach ($tables as $table) {
        echo "Processing table: $table\n";
        
        // Get table structure
        $structureQuery = $pdo->query("DESCRIBE `$table`");
        $columns = $structureQuery->fetchAll(PDO::FETCH_ASSOC);
        
        // Get row count
        $countQuery = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
        $rowCount = $countQuery->fetch(PDO::FETCH_ASSOC)['count'];
        
        $tableStats[$table] = [
            'columns' => count($columns),
            'rows' => $rowCount
        ];
        
        // Create individual table CSV
        $tableFile = fopen($outputDir . "/{$table}_sample.csv", 'w');
        
        // Get column names for CSV header
        $columnNames = array_column($columns, 'Field');
        fputcsv($tableFile, $columnNames);
        
        // Get sample data (limit to 100 rows for manageability)
        $sampleLimit = min(100, $rowCount);
        if ($sampleLimit > 0) {
            $sampleQuery = $pdo->query("SELECT * FROM `$table` LIMIT $sampleLimit");
            while ($row = $sampleQuery->fetch(PDO::FETCH_ASSOC)) {
                fputcsv($tableFile, $row);
            }
        }
        fclose($tableFile);
        
        // Analyze each column
        foreach ($columns as $column) {
            $columnName = $column['Field'];
            $dataType = $column['Type'];
            $isNullable = $column['Null'];
            $defaultValue = $column['Default'];
            $extra = $column['Extra'];
            
            // Get sample values, unique count, min/max for this column
            $sampleValues = [];
            $uniqueCount = 0;
            $minValue = null;
            $maxValue = null;
            
            if ($rowCount > 0) {
                // Get sample values (first 3 non-null values)
                $sampleQuery = $pdo->query("SELECT DISTINCT `$columnName` FROM `$table` WHERE `$columnName` IS NOT NULL LIMIT 3");
                $sampleValues = $sampleQuery->fetchAll(PDO::FETCH_COLUMN);
                
                // Get unique count
                $uniqueQuery = $pdo->query("SELECT COUNT(DISTINCT `$columnName`) as unique_count FROM `$table`");
                $uniqueCount = $uniqueQuery->fetch(PDO::FETCH_ASSOC)['unique_count'];
                
                // Get min/max for numeric/date columns
                if (preg_match('/^(int|bigint|smallint|tinyint|decimal|float|double|datetime|date|timestamp)/i', $dataType)) {
                    try {
                        $minMaxQuery = $pdo->query("SELECT MIN(`$columnName`) as min_val, MAX(`$columnName`) as max_val FROM `$table` WHERE `$columnName` IS NOT NULL");
                        $minMaxResult = $minMaxQuery->fetch(PDO::FETCH_ASSOC);
                        $minValue = $minMaxResult['min_val'];
                        $maxValue = $minMaxResult['max_val'];
                    } catch (Exception $e) {
                        // Skip if there's an error with min/max calculation
                    }
                }
            }
            
            // Write to mapping file
            fputcsv($mappingFile, [
                $table,
                $columnName,
                $dataType,
                $isNullable,
                $defaultValue,
                $extra,
                implode('; ', array_slice($sampleValues, 0, 3)),
                $rowCount,
                $uniqueCount,
                $minValue,
                $maxValue
            ]);
        }
        
        echo "  - Exported $sampleLimit sample rows\n";
        echo "  - Analyzed " . count($columns) . " columns\n\n";
    }
    
    fclose($mappingFile);
    
    // Create summary statistics file
    $summaryFile = fopen($outputDir . '/database_summary.csv', 'w');
    fputcsv($summaryFile, ['Table Name', 'Column Count', 'Row Count', 'Description']);
    
    // Sort tables by row count (descending)
    uasort($tableStats, function($a, $b) {
        return $b['rows'] - $a['rows'];
    });
    
    foreach ($tableStats as $tableName => $stats) {
        $description = getTableDescription($tableName);
        fputcsv($summaryFile, [$tableName, $stats['columns'], $stats['rows'], $description]);
    }
    fclose($summaryFile);
    
    echo "\n=== EXPORT COMPLETE ===\n";
    echo "Files created in '$outputDir/' directory:\n";
    echo "- database_structure_mapping.csv (complete column analysis)\n";
    echo "- database_summary.csv (table statistics)\n";
    echo "- [table_name]_sample.csv (sample data for each table)\n\n";
    
    echo "Top 10 tables by row count:\n";
    $count = 0;
    foreach ($tableStats as $tableName => $stats) {
        if ($count++ >= 10) break;
        echo sprintf("  %s: %s rows, %s columns\n", $tableName, number_format($stats['rows']), $stats['columns']);
    }
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Get a description for common table types
 */
function getTableDescription($tableName) {
    $descriptions = [
        'accommodation' => 'Hotels, resorts and lodging properties',
        'activities' => 'Tours, excursions and activities offered',
        'destinations' => 'Travel destinations and locations',
        'itineraries' => 'Travel itineraries and trip packages',
        'quote_requests' => 'Customer quote and booking requests',
        'contacts' => 'Customer and contact information',
        'images' => 'Image assets and media files',
        'content_blocks' => 'CMS content blocks and text',
        'users' => 'System users and administrators',
        'pages' => 'Static pages and content',
        'holiday_types' => 'Categories of holiday/vacation types',
        'testimonials' => 'Customer reviews and testimonials',
        'faqs' => 'Frequently asked questions',
        'landing_pages' => 'Marketing landing pages',
        'spotlights' => 'Featured content and highlights'
    ];
    
    // Handle WordPress tables
    if (strpos($tableName, 'wp_') === 0) {
        return 'WordPress ' . str_replace('wp_', '', $tableName) . ' table';
    }
    
    // Handle relationship tables
    if (strpos($tableName, '_') !== false) {
        $parts = explode('_', $tableName);
        if (count($parts) >= 2 && isset($descriptions[$parts[0]])) {
            return 'Relationship table linking ' . implode(' and ', $parts);
        }
    }
    
    return $descriptions[$tableName] ?? 'Application data table';
}
?>
