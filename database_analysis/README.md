# Bon Voyage Database Analysis

This folder contains comprehensive analysis of the Bon Voyage database structure and data.

## 📁 Folder Structure

```
database_analysis/
├── README.md                           # This file - main documentation
├── database_mapper.php                 # Database analysis script
├── bon_voyage_database_export.zip      # Complete export archive (417KB)
└── database_export/                    # Database analysis files
    ├── README.md                       # Detailed database documentation
    ├── core_analysis/
    │   ├── database_structure_mapping.csv  # Complete column analysis (914 rows)
    │   └── database_summary.csv           # Table statistics (72 tables)
    └── sample_data/
        └── [table_name]_sample.csv        # Sample data files (71 files)
```

**Note**: URL analysis has been moved to the separate `/url_analysis/` directory in the project root.

## 🚀 Quick Start

### For Data Analysis
1. **Database Overview**: `database_export/core_analysis/database_summary.csv` - All tables summary
2. **Column Details**: `database_export/core_analysis/database_structure_mapping.csv` - Complete schema
3. **Sample Data**: `database_export/sample_data/[table_name]_sample.csv` - Real data examples

### For URL Analysis
**See the separate `/url_analysis/` directory** for complete URL structure analysis including:
- 52 URL patterns mapped
- 1,820 redirect rules analyzed
- Real URL examples from database
- Complete routing documentation

### For Development/Migration
1. **Database**: `database_export/README.md` - Technical database analysis
2. **URLs**: `url_analysis/README.md` - URL structure and routing guide
3. **Scripts**: `database_mapper.php` & `url_structure_analyzer.php` - Regenerate analysis
4. **Archive**: `bon_voyage_database_export.zip` - Complete portable export

## 📊 Analysis Overview

### Database Analysis
- **71 tables** total (47 core application + 24 WordPress)
- **51,484+ records** across all tables
- **914 columns** analyzed with full type information

#### Top Tables by Size
1. `quote_requests` - 51,484 rows (Customer bookings)
2. `images` - 16,203 rows (Media assets)
3. `contacts` - 8,402 rows (Customer data)
4. `content_blocks` - 6,422 rows (CMS content)
5. `itinerary_days` - 4,201 rows (Trip details)

### URL Analysis
- **52 URL patterns** mapped across all sections
- **~21,246 estimated total URLs** in the system
- **1,820 redirect rules** for legacy URL handling
- **10 rewrite rules** for routing and assets

#### URL Structure
- **429 destinations** (e.g., usa_holidays, alaska_holidays)
- **16 holiday types** (e.g., fly_drive_holidays, escorted_tours)
- **665 accommodations** (hotels, resorts, lodges)
- **250 itineraries** (tour packages, trips)
- **565 activities** (tours, excursions, experiences)
- **105 testimonials** (customer reviews)
- **38 landing pages** (marketing campaigns)
- **45 static pages** (about, policies, information)

## 🔧 Usage

### Regenerating the Analysis
```bash
# From the bon-voyage root directory
ddev exec php database_analysis/database_mapper.php      # Database analysis
ddev exec php database_analysis/url_structure_analyzer.php  # URL analysis
```

### Accessing Sample Data
Each table has a corresponding `[table_name]_sample.csv` file with up to 100 representative rows.

### Understanding Structure
- `database_structure_mapping.csv` contains complete column analysis
- Includes data types, constraints, sample values, and statistics
- Min/max values for numeric fields, unique counts, etc.

## 📋 Key Insights

### Data Architecture
- **Hierarchical destinations** using nested set model
- **Many-to-many relationships** via junction tables
- **Comprehensive metadata** for SEO and content management
- **WordPress integration** for blog functionality

### Common Patterns
- Primary keys: `int(10) unsigned auto_increment`
- Timestamps: `datetime` for created/modified tracking
- Flags: `tinyint(1)` for boolean values
- Slugs: `varchar(255)` for URL-friendly identifiers
- Content: `text` fields with HTML support

## 📈 Data Quality
- High completeness in core tables
- Consistent naming conventions
- Active content management (recent modifications)
- Well-structured relationships

---

*Generated on: September 3, 2025*
*Database export: 1.4MB (compressed: 417KB)*
*URL analysis: 296KB (6 files)*
*Total analysis: ~1.7MB*
