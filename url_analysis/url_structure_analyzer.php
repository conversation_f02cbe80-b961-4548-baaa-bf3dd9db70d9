<?php
/**
 * Bon Voyage URL Structure Analyzer
 * Analyzes all URL patterns, routes, and generates comprehensive URL mapping
 */

// Database configuration
$host = 'db';
$username = 'db';
$password = 'db';
$database = 'db';

// Output directory
$outputDir = 'output';
if (!is_dir($outputDir)) {
    mkdir($outputDir, 0755, true);
}

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Connected to database successfully.\n";
    echo "Analyzing URL structures...\n\n";

    // Create comprehensive URL mapping
    $urlMappingFile = fopen($outputDir . '/complete_url_mapping.csv', 'w');
    fputcsv($urlMappingFile, [
        'URL Pattern',
        'Example URL',
        'Controller',
        'Action',
        'Section',
        'Description',
        'URL Type',
        'Parameters',
        'Sample Count'
    ]);

    $urlPatterns = [];

    // 1. STATIC ROUTES
    $staticRoutes = [
        ['/', '/', 'pages', 'home', '', 'Homepage', 'Static', '', 1],
        ['/mmenu', '/mmenu', 'pages', 'mmenu', '', 'Mobile menu API', 'API', '', 1],
        ['/megamenu', '/megamenu', 'pages', 'megamenu', '', 'Desktop menu API', 'API', '', 1],
        ['/cookies', '/cookies', 'cookies', 'preferences', '', 'Cookie preferences', 'Static', 'plugin=cookies', 1],
        ['/faqs', '/faqs', 'faqs', 'index', '', 'FAQ listing', 'Static', 'plugin=just_faqs', 1],
        ['/search', '/search', 'search', 'results', '', 'Search results', 'Static', 'plugin=site_search', 1],
        ['/start_planning_now', '/start_planning_now', 'travel_plans', 'add', '', 'Travel planning form', 'Static', '', 1],
        ['/make_an_enquiry', '/make_an_enquiry', 'travel_plans', 'add_lite', '', 'Quick enquiry form', 'Static', '', 1],
        ['/social', '/social', 'travel_plans', 'add_social', '', 'Social travel planning', 'Static', '', 1],
        ['/page/testimonials', '/page/testimonials', 'testimonials', 'index', '', 'Testimonials listing', 'Static', '', 1],
        ['/page/the_bon_voyage_holiday_promise', '/page/the_bon_voyage_holiday_promise', 'promise', 'index', '', 'Holiday promise page', 'Static', '', 1],
    ];

    foreach ($staticRoutes as $route) {
        fputcsv($urlMappingFile, $route);
        $urlPatterns[] = $route;
    }

    // 2. DESTINATIONS
    echo "Analyzing destinations...\n";
    $destinationsQuery = $pdo->query("SELECT slug, name FROM destinations WHERE published = 1 AND slug IS NOT NULL ORDER BY name");
    $destinations = $destinationsQuery->fetchAll(PDO::FETCH_ASSOC);

    $destCount = count($destinations);
    echo "Found $destCount published destinations\n";

    // Destination patterns
    $destinationPatterns = [
        ['/destinations', '/destinations', 'destinations', 'view', 'destinations', 'All destinations listing', 'Listing', '', $destCount],
        ['/destinations/:destination_slug', '/destinations/usa_holidays', 'destinations', 'view', 'destinations', 'Individual destination page', 'Dynamic', 'destination_slug', $destCount],
        ['/destinations/:destination_slug/accommodation', '/destinations/usa_holidays/accommodation', 'accommodations', 'index', 'destinations', 'Destination accommodation listing', 'Dynamic', 'destination_slug', $destCount],
        ['/destinations/:destination_slug/accommodation/:accommodation_slug', '/destinations/usa_holidays/accommodation/hotel_name', 'accommodations', 'view', 'destinations', 'Specific accommodation in destination', 'Dynamic', 'destination_slug,accommodation_slug', 0],
        ['/destinations/:destination_slug/itineraries', '/destinations/usa_holidays/itineraries', 'itineraries', 'index', 'destinations', 'Destination itineraries listing', 'Dynamic', 'destination_slug', $destCount],
        ['/destinations/:destination_slug/itinerary/:itinerary_slug', '/destinations/usa_holidays/itinerary/grand_tour', 'itineraries', 'view', 'destinations', 'Specific itinerary in destination', 'Dynamic', 'destination_slug,itinerary_slug', 0],
        ['/destinations/:destination_slug/activities', '/destinations/usa_holidays/activities', 'activities', 'index', 'destinations', 'Destination activities listing', 'Dynamic', 'destination_slug', $destCount],
        ['/destinations/:destination_slug/activity/:activity_slug', '/destinations/usa_holidays/activity/tour_name', 'activities', 'view', 'destinations', 'Specific activity in destination', 'Dynamic', 'destination_slug,activity_slug', 0],
        ['/destinations/:destination_slug/images', '/destinations/usa_holidays/images', 'images', 'index', 'destinations', 'Destination image gallery', 'Dynamic', 'destination_slug', $destCount],
        ['/destinations/:destination_slug/videos', '/destinations/usa_holidays/videos', 'youtube_videos', 'index', 'destinations', 'Destination video gallery', 'Dynamic', 'destination_slug', $destCount],
    ];

    foreach ($destinationPatterns as $pattern) {
        fputcsv($urlMappingFile, $pattern);
        $urlPatterns[] = $pattern;
    }

    // 3. HOLIDAY TYPES
    echo "Analyzing holiday types...\n";
    $holidayTypesQuery = $pdo->query("SELECT slug, name FROM holiday_types WHERE published = 1 AND slug IS NOT NULL ORDER BY name");
    $holidayTypes = $holidayTypesQuery->fetchAll(PDO::FETCH_ASSOC);

    $holidayCount = count($holidayTypes);
    echo "Found $holidayCount published holiday types\n";

    $holidayPatterns = [
        ['/holidays', '/holidays', 'holiday_types', 'index', 'holidays', 'All holiday types listing', 'Listing', '', $holidayCount],
        ['/holidays/:holiday_type_slug', '/holidays/fly_drive_holidays', 'holiday_types', 'view', 'holidays', 'Individual holiday type page', 'Dynamic', 'holiday_type_slug', $holidayCount],
        ['/holidays/:holiday_type_slug/destinations', '/holidays/fly_drive_holidays/destinations', 'destinations', 'index', 'holidays', 'Holiday type destinations listing', 'Dynamic', 'holiday_type_slug', $holidayCount],
        ['/holidays/:holiday_type_slug/destinations/:destination_slug', '/holidays/fly_drive_holidays/destinations/usa_holidays', 'destinations', 'view', 'holidays', 'Destination within holiday type', 'Dynamic', 'holiday_type_slug,destination_slug', 0],
        ['/holidays/:holiday_type_slug/accommodation', '/holidays/fly_drive_holidays/accommodation', 'accommodations', 'index', 'holidays', 'Holiday type accommodation listing', 'Dynamic', 'holiday_type_slug', $holidayCount],
        ['/holidays/:holiday_type_slug/accommodation/:accommodation_slug', '/holidays/fly_drive_holidays/accommodation/hotel_name', 'accommodations', 'view', 'holidays', 'Accommodation within holiday type', 'Dynamic', 'holiday_type_slug,accommodation_slug', 0],
        ['/holidays/:holiday_type_slug/itineraries', '/holidays/fly_drive_holidays/itineraries', 'itineraries', 'index', 'holidays', 'Holiday type itineraries listing', 'Dynamic', 'holiday_type_slug', $holidayCount],
        ['/holidays/:holiday_type_slug/itinerary/:itinerary_slug', '/holidays/fly_drive_holidays/itinerary/grand_tour', 'itineraries', 'view', 'holidays', 'Itinerary within holiday type', 'Dynamic', 'holiday_type_slug,itinerary_slug', 0],
        ['/holidays/:holiday_type_slug/activities', '/holidays/fly_drive_holidays/activities', 'activities', 'index', 'holidays', 'Holiday type activities listing', 'Dynamic', 'holiday_type_slug', $holidayCount],
        ['/holidays/:holiday_type_slug/activity/:activity_slug', '/holidays/fly_drive_holidays/activity/tour_name', 'activities', 'view', 'holidays', 'Activity within holiday type', 'Dynamic', 'holiday_type_slug,activity_slug', 0],
    ];

    foreach ($holidayPatterns as $pattern) {
        fputcsv($urlMappingFile, $pattern);
        $urlPatterns[] = $pattern;
    }

    // 4. CANONICAL ROUTES (Direct access)
    echo "Analyzing canonical routes...\n";
    $accommodationsQuery = $pdo->query("SELECT COUNT(*) as count FROM accommodation WHERE published = 1 AND slug IS NOT NULL");
    $accommodationCount = $accommodationsQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $itinerariesQuery = $pdo->query("SELECT COUNT(*) as count FROM itineraries WHERE published = 1 AND slug IS NOT NULL");
    $itineraryCount = $itinerariesQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $activitiesQuery = $pdo->query("SELECT COUNT(*) as count FROM activities WHERE published = 1 AND slug IS NOT NULL");
    $activityCount = $activitiesQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $testimonialsQuery = $pdo->query("SELECT COUNT(*) as count FROM testimonials WHERE published = 1 AND slug IS NOT NULL");
    $testimonialCount = $testimonialsQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $canonicalPatterns = [
        ['/accommodations/:accommodation_slug', '/accommodations/hotel_name', 'accommodations', 'view', 'destinations', 'Direct accommodation access', 'Canonical', 'accommodation_slug', $accommodationCount],
        ['/itineraries/:itinerary_slug', '/itineraries/grand_tour', 'itineraries', 'view', 'destinations', 'Direct itinerary access', 'Canonical', 'itinerary_slug', $itineraryCount],
        ['/activities/:activity_slug', '/activities/tour_name', 'activities', 'view', 'destinations', 'Direct activity access', 'Canonical', 'activity_slug', $activityCount],
        ['/testimonials/:testimonial_slug', '/testimonials/customer_review', 'testimonials', 'view', 'destinations', 'Direct testimonial access', 'Canonical', 'testimonial_slug', $testimonialCount],
        ['/testimonial/:testimonial_slug', '/testimonial/customer_review', 'testimonials', 'view', 'testimonials', 'Alternative testimonial access', 'Canonical', 'testimonial_slug', $testimonialCount],
    ];

    foreach ($canonicalPatterns as $pattern) {
        fputcsv($urlMappingFile, $pattern);
        $urlPatterns[] = $pattern;
    }

    echo "Found $accommodationCount accommodations, $itineraryCount itineraries, $activityCount activities, $testimonialCount testimonials\n";

    // 5. CAMPAIGNS/LANDING PAGES
    echo "Analyzing landing pages...\n";
    $landingPagesQuery = $pdo->query("SELECT slug, name FROM landing_pages WHERE published = 1 AND slug IS NOT NULL ORDER BY name");
    $landingPages = $landingPagesQuery->fetchAll(PDO::FETCH_ASSOC);

    $landingCount = count($landingPages);
    echo "Found $landingCount published landing pages\n";

    $campaignPatterns = [
        ['/campaigns', '/campaigns', 'landing_pages', 'index', 'landing_pages', 'All campaigns listing', 'Listing', '', $landingCount],
        ['/campaigns/:landing_page_slug', '/campaigns/special_offer', 'landing_pages', 'view', 'landing_pages', 'Individual campaign page', 'Dynamic', 'landing_page_slug', $landingCount],
        ['/campaigns/:landing_page_slug/destinations', '/campaigns/special_offer/destinations', 'destinations', 'index', 'landing_pages', 'Campaign destinations', 'Dynamic', 'landing_page_slug', $landingCount],
        ['/campaigns/:landing_page_slug/accommodation', '/campaigns/special_offer/accommodation', 'accommodations', 'index', 'landing_pages', 'Campaign accommodation', 'Dynamic', 'landing_page_slug', $landingCount],
        ['/campaigns/:landing_page_slug/itineraries', '/campaigns/special_offer/itineraries', 'itineraries', 'index', 'landing_pages', 'Campaign itineraries', 'Dynamic', 'landing_page_slug', $landingCount],
        ['/campaigns/:landing_page_slug/itinerary/:itinerary_slug', '/campaigns/special_offer/itinerary/grand_tour', 'itineraries', 'view', 'landing_pages', 'Campaign specific itinerary', 'Dynamic', 'landing_page_slug,itinerary_slug', 0],
    ];

    foreach ($campaignPatterns as $pattern) {
        fputcsv($urlMappingFile, $pattern);
        $urlPatterns[] = $pattern;
    }

    // 6. PAGES
    echo "Analyzing static pages...\n";
    $pagesQuery = $pdo->query("SELECT slug, navigation_label FROM pages WHERE published = 1 AND slug IS NOT NULL ORDER BY navigation_label");
    $pages = $pagesQuery->fetchAll(PDO::FETCH_ASSOC);

    $pageCount = count($pages);
    echo "Found $pageCount published pages\n";

    $pagePatterns = [
        ['/page/:page_slug', '/page/about_us', 'pages', 'view', '', 'Static content pages', 'Dynamic', 'page_slug', $pageCount],
    ];

    foreach ($pagePatterns as $pattern) {
        fputcsv($urlMappingFile, $pattern);
        $urlPatterns[] = $pattern;
    }

    // 7. ADDITIONAL ROUTES
    $additionalPatterns = [
        ['/search/:term', '/search/florida', 'search', 'results', '', 'Search with term', 'Dynamic', 'term', 1],
        ['/img/uploads/:id_:version.:extension', '/img/uploads/123_medium.jpg', 'images', 'cache_and_serve', '', 'Image serving', 'Dynamic', 'id,version,extension', 16203],
        ['/in_the_press', '/in_the_press', 'press_releases', 'index', '', 'Press releases listing', 'Listing', '', 1],
        ['/in_the_press/:press_release_slug', '/in_the_press/latest_news', 'press_releases', 'view', '', 'Individual press release', 'Dynamic', 'press_release_slug', 12],
        ['/api/navigation/megamenu', '/api/navigation/megamenu', 'navigation', 'megamenu', '', 'Navigation API - desktop', 'API', '', 1],
        ['/api/navigation/mmenu', '/api/navigation/mmenu', 'navigation', 'mmenu', '', 'Navigation API - mobile', 'API', '', 1],
        ['/webadmin', '/webadmin', 'users', 'home', '', 'Admin panel home', 'Admin', 'webadmin=true', 1],
        ['/proxy/:action', '/proxy/some_action', 'proxy', 'dynamic', '', 'Proxy endpoints', 'API', 'action', 1],
        ['/v1/*', '/v1/geocode', 'itineraries', 'yahooGeoProxy', '', 'Legacy Yahoo Geo API proxy', 'API', '', 1],
    ];

    foreach ($additionalPatterns as $pattern) {
        fputcsv($urlMappingFile, $pattern);
        $urlPatterns[] = $pattern;
    }

    // 8. GENERATE SAMPLE URLS
    echo "Generating sample URLs...\n";
    $sampleUrlsFile = fopen($outputDir . '/sample_urls.csv', 'w');
    fputcsv($sampleUrlsFile, ['URL', 'Type', 'Description', 'Status']);

    // Sample destinations
    $sampleDestinations = array_slice($destinations, 0, 10);
    foreach ($sampleDestinations as $dest) {
        fputcsv($sampleUrlsFile, ["/destinations/{$dest['slug']}", 'Destination', $dest['name'], 'Active']);
        fputcsv($sampleUrlsFile, ["/destinations/{$dest['slug']}/accommodation", 'Destination Accommodation', "{$dest['name']} Hotels", 'Active']);
        fputcsv($sampleUrlsFile, ["/destinations/{$dest['slug']}/itineraries", 'Destination Itineraries', "{$dest['name']} Tours", 'Active']);
    }

    // Sample holiday types
    $sampleHolidays = array_slice($holidayTypes, 0, 5);
    foreach ($sampleHolidays as $holiday) {
        fputcsv($sampleUrlsFile, ["/holidays/{$holiday['slug']}", 'Holiday Type', $holiday['name'], 'Active']);
        fputcsv($sampleUrlsFile, ["/holidays/{$holiday['slug']}/destinations", 'Holiday Destinations', "{$holiday['name']} Destinations", 'Active']);
    }

    // Sample landing pages
    $sampleLanding = array_slice($landingPages, 0, 5);
    foreach ($sampleLanding as $landing) {
        fputcsv($sampleUrlsFile, ["/campaigns/{$landing['slug']}", 'Campaign', $landing['name'], 'Active']);
    }

    // Sample pages
    $samplePages = array_slice($pages, 0, 10);
    foreach ($samplePages as $page) {
        if ($page['slug']) {
            fputcsv($sampleUrlsFile, ["/page/{$page['slug']}", 'Static Page', $page['navigation_label'] ?: 'Page', 'Active']);
        }
    }

    fclose($sampleUrlsFile);

    echo "\n=== URL ANALYSIS COMPLETE ===\n";
    echo "Total URL patterns analyzed: " . count($urlPatterns) . "\n";
    echo "Files created in '$outputDir/' directory:\n";
    echo "- complete_url_mapping.csv (comprehensive URL analysis)\n";
    echo "- sample_urls.csv (real URL examples)\n\n";

    // Summary statistics
    $totalUrls = 0;
    $urlTypes = [];
    foreach ($urlPatterns as $pattern) {
        $totalUrls += $pattern[8]; // Sample count
        $type = $pattern[6]; // URL Type
        $urlTypes[$type] = ($urlTypes[$type] ?? 0) + 1;
    }

    echo "URL Pattern Summary:\n";
    foreach ($urlTypes as $type => $count) {
        echo "  $type: $count patterns\n";
    }
    echo "\nEstimated total URLs: " . number_format($totalUrls) . "\n";

} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
