# Bon Voyage URL Structure Analysis

This directory contains a comprehensive analysis of all URL structures, routing patterns, and redirects in the Bon Voyage website.

## 📁 Directory Structure

```
url_analysis/
├── README.md                           # This documentation
├── COMPLETE_URL_LIST.md                # Complete URL list documentation
├── DUPLICATE_ROUTES.md                 # Duplicate routes analysis ⭐ **NEW**
├── PLACEHOLDER_EXPANSION.md            # Placeholder URL expansion ⭐ **NEW**
├── run_analysis.php                    # Complete analysis runner script
├── url_structure_analyzer.php          # Main URL analysis script
├── complete_url_generator.php          # Complete URL list generator
├── duplicate_routes_analyzer.php       # Duplicate routes analyzer ⭐ **NEW**
├── expand_placeholder_urls.php         # Placeholder URL expander ⭐ **NEW**
├── .gitignore                          # Version control settings
├── output/                             # Analysis results
│   ├── .gitkeep                        # Ensures directory tracking
│   ├── complete_url_mapping.csv        # URL pattern analysis (52 patterns)
│   ├── complete_url_list.csv           # Complete URL list (2,967 URLs)
│   ├── duplicate_routes.csv            # Duplicate routes summary ⭐ **NEW**
│   ├── detailed_duplicate_urls.csv     # Specific duplicate examples ⭐ **NEW**
│   ├── sample_urls.csv                 # Real URL examples (57 samples)
│   ├── htaccess_redirects.csv          # Redirect rules (1,820 redirects)
│   └── htaccess_rewrites.csv           # Rewrite rules (10 rules)
└── scripts/                            # Additional analysis tools
    └── htaccess_redirect_analyzer.php  # .htaccess redirect analyzer
```

## 📊 Analysis Files

### Core Analysis (in `output/`)
- **`complete_url_mapping.csv`** - URL pattern analysis (52 patterns)
- **`complete_url_list.csv`** - Complete URL list (3,033 URLs with domain) ⭐ **EXPANDED**
- **`complete_url_list_expanded.csv`** - Expanded URL list (backup) ⭐ **NEW**
- **`complete_url_list_original.csv`** - Original URL list (backup) ⭐ **NEW**
- **`duplicate_routes.csv`** - Duplicate routes summary (9 groups) ⭐ **NEW**
- **`detailed_duplicate_urls.csv`** - Specific duplicate examples ⭐ **NEW**
- **`sample_urls.csv`** - Real URL examples (57 samples)
- **`htaccess_redirects.csv`** - Redirect rules (1,820 redirects)
- **`htaccess_rewrites.csv`** - Rewrite rules (10 rules)

### Analysis Scripts
- **`run_analysis.php`** - Complete analysis runner (runs all scripts)
- **`url_structure_analyzer.php`** - Main URL analysis script
- **`complete_url_generator.php`** - Complete URL list generator ⭐ **NEW**
- **`duplicate_routes_analyzer.php`** - Duplicate routes analyzer ⭐ **NEW**
- **`expand_placeholder_urls.php`** - Placeholder URL expander ⭐ **NEW**
- **`scripts/htaccess_redirect_analyzer.php`** - .htaccess redirect analyzer

## 🚀 Quick Start

### Run Complete Analysis
```bash
# From the bon-voyage root directory
ddev exec "cd url_analysis && php run_analysis.php"
```

### Run Individual Scripts
```bash
# URL structure analysis only
ddev exec "cd url_analysis && php url_structure_analyzer.php"

# Complete URL list generation
ddev exec "cd url_analysis && php complete_url_generator.php"

# Duplicate routes analysis
ddev exec "cd url_analysis && php duplicate_routes_analyzer.php"

# Expand placeholder URLs with actual entries
ddev exec "cd url_analysis && php expand_placeholder_urls.php"

# .htaccess redirect analysis only
ddev exec "cd url_analysis/scripts && php htaccess_redirect_analyzer.php"
```

## 🌐 URL Architecture Overview

### Primary URL Patterns

#### 1. **Static Routes** (11 patterns)
- `/` - Homepage
- `/mmenu` - Mobile menu API
- `/megamenu` - Desktop menu API  
- `/faqs` - FAQ listing
- `/search` - Search functionality
- `/start_planning_now` - Travel planning form
- `/make_an_enquiry` - Quick enquiry form
- `/page/testimonials` - Testimonials
- `/cookies` - Cookie preferences

#### 2. **Destinations** (10 patterns, 429 destinations)
- `/destinations` - All destinations listing
- `/destinations/:destination_slug` - Individual destination
- `/destinations/:destination_slug/accommodation` - Destination hotels
- `/destinations/:destination_slug/accommodation/:accommodation_slug` - Specific hotel
- `/destinations/:destination_slug/itineraries` - Destination tours
- `/destinations/:destination_slug/itinerary/:itinerary_slug` - Specific tour
- `/destinations/:destination_slug/activities` - Destination activities
- `/destinations/:destination_slug/activity/:activity_slug` - Specific activity
- `/destinations/:destination_slug/images` - Destination gallery
- `/destinations/:destination_slug/videos` - Destination videos

#### 3. **Holiday Types** (10 patterns, 16 types)
- `/holidays` - All holiday types
- `/holidays/:holiday_type_slug` - Individual holiday type
- `/holidays/:holiday_type_slug/destinations` - Holiday destinations
- `/holidays/:holiday_type_slug/destinations/:destination_slug` - Specific destination in holiday
- `/holidays/:holiday_type_slug/accommodation` - Holiday accommodation
- `/holidays/:holiday_type_slug/accommodation/:accommodation_slug` - Specific hotel in holiday
- `/holidays/:holiday_type_slug/itineraries` - Holiday itineraries
- `/holidays/:holiday_type_slug/itinerary/:itinerary_slug` - Specific itinerary in holiday
- `/holidays/:holiday_type_slug/activities` - Holiday activities
- `/holidays/:holiday_type_slug/activity/:activity_slug` - Specific activity in holiday

#### 4. **Canonical Routes** (5 patterns)
Direct access to content without context:
- `/accommodations/:accommodation_slug` - Direct hotel access (665 hotels)
- `/itineraries/:itinerary_slug` - Direct tour access (250 tours)
- `/activities/:activity_slug` - Direct activity access (565 activities)
- `/testimonials/:testimonial_slug` - Direct testimonial access (105 testimonials)
- `/testimonial/:testimonial_slug` - Alternative testimonial access

#### 5. **Campaigns/Landing Pages** (6 patterns, 38 campaigns)
- `/campaigns` - All campaigns listing
- `/campaigns/:landing_page_slug` - Individual campaign
- `/campaigns/:landing_page_slug/destinations` - Campaign destinations
- `/campaigns/:landing_page_slug/accommodation` - Campaign hotels
- `/campaigns/:landing_page_slug/itineraries` - Campaign tours
- `/campaigns/:landing_page_slug/itinerary/:itinerary_slug` - Specific campaign tour

#### 6. **Static Pages** (1 pattern, 45 pages)
- `/page/:page_slug` - Static content pages

#### 7. **Additional Routes** (9 patterns)
- `/search/:term` - Search with specific term
- `/img/uploads/:id_:version.:extension` - Image serving (16,203 images)
- `/in_the_press` - Press releases listing
- `/in_the_press/:press_release_slug` - Individual press release
- `/api/navigation/megamenu` - Navigation API (desktop)
- `/api/navigation/mmenu` - Navigation API (mobile)
- `/webadmin` - Admin panel
- `/proxy/:action` - Proxy endpoints
- `/v1/*` - Legacy Yahoo Geo API proxy

## 📊 URL Statistics

### Content Volume
- **429 destinations** (e.g., usa_holidays, alaska_holidays, california_holidays)
- **16 holiday types** (e.g., fly_drive_holidays, escorted_tours_and_holidays)
- **665 accommodations** (hotels, resorts, lodges)
- **250 itineraries** (tour packages, trips)
- **565 activities** (tours, excursions, experiences)
- **105 testimonials** (customer reviews)
- **38 landing pages** (marketing campaigns)
- **45 static pages** (about, policies, information)
- **16,203 images** (photos, galleries)

### Estimated Total URLs
**~21,246 unique URLs** across all patterns and content

### URL Pattern Types
- **Static**: 11 patterns (fixed URLs)
- **Dynamic**: 27 patterns (parameter-based)
- **Listing**: 4 patterns (index/overview pages)
- **Canonical**: 5 patterns (direct content access)
- **API**: 4 patterns (data endpoints)
- **Admin**: 1 pattern (backend access)

## 🔄 Redirect Analysis

### .htaccess Redirects (1,820 total)
The site maintains extensive redirects for URL restructuring:

#### Redirect Categories
- **Destination Restructure**: 837 redirects (46%)
- **Holiday Type Redirect**: 300 redirects (16%)
- **Content Restructure**: 243 redirects (13%)
- **Destination Consolidation**: 173 redirects (10%)
- **Campaign Redirect**: 63 redirects (3%)
- **Activity Redirect**: 63 redirects (3%)
- **General Redirect**: 54 redirects (3%)
- **Itinerary Redirect**: 47 redirects (3%)
- **Legacy Format**: 17 redirects (1%)
- **External Redirect**: 12 redirects (1%)
- **Blog Redirect**: 10 redirects (1%)
- **Accommodation Redirect**: 1 redirect (<1%)

### Common Redirect Patterns
- Legacy `.html` and `.asp` files → Modern URLs
- Old destination structures → New hierarchical structure
- Consolidated destinations (e.g., multiple city pages → single region page)
- Campaign URL changes
- Content reorganization (spotlights → whats_hot)

## 🏗️ Technical Implementation

### Routing System
- **CakePHP 1.2** routing with `routes.php` configuration
- **WordPress** blog routing for `/blog/*` paths
- **Plugin-based** routing for specialized functionality
- **Section-based** routing (destinations, holidays, landing_pages)

### URL Patterns
- **Slug-based** URLs for SEO-friendly paths
- **Hierarchical** structure (parent/child relationships)
- **Multi-section** access (same content via different contexts)
- **RESTful** patterns for API endpoints

### Key Features
- **Canonical URLs** for direct content access
- **Contextual URLs** for content within sections
- **API endpoints** for navigation and data
- **Image serving** with version control
- **Search functionality** with clean URLs
- **Admin interface** with separate routing

This analysis provides a complete foundation for understanding the Bon Voyage URL architecture, planning migrations, or implementing new features while maintaining URL consistency and SEO value.
