<?php
/**
 * .htaccess Redirect Analyzer
 * Extracts and analyzes all URL redirects from .htaccess files
 */

$htaccessFile = '../../app/webroot/.htaccess';
$outputDir = '../output';

if (!file_exists($htaccessFile)) {
    die("Error: .htaccess file not found at $htaccessFile\n");
}

echo "Analyzing .htaccess redirects...\n";

$content = file_get_contents($htaccessFile);
$lines = explode("\n", $content);

$redirects = [];
$rewriteRules = [];

foreach ($lines as $lineNum => $line) {
    $line = trim($line);

    // Skip comments and empty lines
    if (empty($line) || strpos($line, '#') === 0) {
        continue;
    }

    // Look for RewriteRule patterns
    if (preg_match('/^RewriteRule\s+(.+?)\s+(.+?)\s+\[(.+?)\]/', $line, $matches)) {
        $pattern = $matches[1];
        $target = $matches[2];
        $flags = $matches[3];

        // Only include redirects (R=301, R=302, etc.)
        if (strpos($flags, 'R=') !== false || strpos($flags, 'R,') !== false) {
            $redirects[] = [
                'line' => $lineNum + 1,
                'pattern' => $pattern,
                'target' => $target,
                'flags' => $flags,
                'type' => 'Redirect'
            ];
        } else {
            $rewriteRules[] = [
                'line' => $lineNum + 1,
                'pattern' => $pattern,
                'target' => $target,
                'flags' => $flags,
                'type' => 'Rewrite'
            ];
        }
    }
}

echo "Found " . count($redirects) . " redirects and " . count($rewriteRules) . " rewrite rules\n";

// Create redirects CSV
$redirectsFile = fopen($outputDir . '/htaccess_redirects.csv', 'w');
fputcsv($redirectsFile, ['Line', 'From Pattern', 'To Target', 'Flags', 'Type', 'Category']);

foreach ($redirects as $redirect) {
    $category = categorizeRedirect($redirect['pattern'], $redirect['target']);
    fputcsv($redirectsFile, [
        $redirect['line'],
        $redirect['pattern'],
        $redirect['target'],
        $redirect['flags'],
        $redirect['type'],
        $category
    ]);
}

fclose($redirectsFile);

// Create rewrite rules CSV
$rewriteFile = fopen($outputDir . '/htaccess_rewrites.csv', 'w');
fputcsv($rewriteFile, ['Line', 'Pattern', 'Target', 'Flags', 'Type', 'Purpose']);

foreach ($rewriteRules as $rule) {
    $purpose = categorizeRewrite($rule['pattern'], $rule['target']);
    fputcsv($rewriteFile, [
        $rule['line'],
        $rule['pattern'],
        $rule['target'],
        $rule['flags'],
        $rule['type'],
        $purpose
    ]);
}

fclose($rewriteFile);

// Analyze redirect patterns
$categories = [];
foreach ($redirects as $redirect) {
    $category = categorizeRedirect($redirect['pattern'], $redirect['target']);
    $categories[$category] = ($categories[$category] ?? 0) + 1;
}

echo "\nRedirect Categories:\n";
foreach ($categories as $category => $count) {
    echo "  $category: $count redirects\n";
}

echo "\nFiles created:\n";
echo "- htaccess_redirects.csv (" . count($redirects) . " redirects)\n";
echo "- htaccess_rewrites.csv (" . count($rewriteRules) . " rewrite rules)\n";

function categorizeRedirect($pattern, $target) {
    // Analyze the pattern and target to categorize the redirect

    if (strpos($pattern, 'destinations') !== false) {
        if (strpos($target, 'destinations') !== false) {
            return 'Destination Restructure';
        } else {
            return 'Destination Consolidation';
        }
    }

    if (strpos($pattern, 'holidays') !== false) {
        return 'Holiday Type Redirect';
    }

    if (strpos($pattern, 'campaigns') !== false) {
        return 'Campaign Redirect';
    }

    if (strpos($pattern, 'whats_hot') !== false || strpos($pattern, 'spotlights') !== false) {
        return 'Content Restructure';
    }

    if (strpos($pattern, 'blog') !== false) {
        return 'Blog Redirect';
    }

    if (strpos($target, 'http') === 0) {
        return 'External Redirect';
    }

    if (strpos($pattern, '\.html') !== false || strpos($pattern, '\.asp') !== false) {
        return 'Legacy Format';
    }

    if (strpos($pattern, 'accommodation') !== false) {
        return 'Accommodation Redirect';
    }

    if (strpos($pattern, 'itinerary') !== false || strpos($pattern, 'itineraries') !== false) {
        return 'Itinerary Redirect';
    }

    if (strpos($pattern, 'activity') !== false || strpos($pattern, 'activities') !== false) {
        return 'Activity Redirect';
    }

    return 'General Redirect';
}

function categorizeRewrite($pattern, $target) {
    if (strpos($target, 'blog/index.php') !== false) {
        return 'WordPress Blog Routing';
    }

    if (strpos($target, 'index.php') !== false) {
        return 'CakePHP Routing';
    }

    if (strpos($pattern, 'css') !== false || strpos($pattern, 'js') !== false) {
        return 'Asset Versioning';
    }

    if (strpos($pattern, 'build') !== false) {
        return 'Build Asset Handling';
    }

    return 'General Rewrite';
}
