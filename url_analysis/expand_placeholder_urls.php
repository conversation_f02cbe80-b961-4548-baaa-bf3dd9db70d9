<?php
/**
 * Expand Placeholder URLs
 * Finds actual entries for URLs with [slug], [id], etc. placeholders
 * and adds them to the complete URL list
 */

// Database configuration
$host = 'db';
$username = 'db';
$password = 'db';
$database = 'db';
$domain = 'https://www.bon-voyage.co.uk';

// Output directory
$outputDir = 'output';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Connected to database successfully.\n";
    echo "Expanding placeholder URLs with actual entries...\n\n";

    // Read existing complete URL list
    $existingUrls = [];
    $existingFile = fopen($outputDir . '/complete_url_list.csv', 'r');
    $header = fgetcsv($existingFile);

    while (($row = fgetcsv($existingFile)) !== FALSE) {
        $existingUrls[] = $row;
    }
    fclose($existingFile);

    // Create expanded URL list
    $expandedFile = fopen($outputDir . '/complete_url_list_expanded.csv', 'w');
    fputcsv($expandedFile, $header);

    $addedUrls = 0;
    $placeholderUrls = [];

    // Process each existing URL
    foreach ($existingUrls as $urlRow) {
        $url = $urlRow[0];
        $urlType = $urlRow[1];
        $section = $urlRow[2];
        $description = $urlRow[3];
        $status = $urlRow[4];
        $contentCount = $urlRow[5];

        // Check if this is a placeholder URL
        if (strpos($url, '[slug]') !== false || strpos($url, '[id]') !== false ||
            strpos($url, '[term]') !== false || strpos($url, '[action]') !== false ||
            strpos($url, '[endpoint]') !== false || strpos($url, '[version]') !== false) {

            $placeholderUrls[] = $urlRow;

            // Expand based on URL pattern
            if (strpos($url, '/accommodations/[slug]') !== false) {
                echo "Expanding accommodation URLs...\n";
                $query = $pdo->query("SELECT slug, name FROM accommodation WHERE published = 1 AND slug IS NOT NULL ORDER BY name LIMIT 10");
                $items = $query->fetchAll(PDO::FETCH_ASSOC);

                foreach ($items as $item) {
                    $expandedUrl = str_replace('[slug]', $item['slug'], $url);
                    $expandedDescription = "Direct access to {$item['name']}";
                    fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                    $addedUrls++;
                }

            } elseif (strpos($url, '/itineraries/[slug]') !== false) {
                echo "Expanding itinerary URLs...\n";
                $query = $pdo->query("SELECT slug, name FROM itineraries WHERE published = 1 AND slug IS NOT NULL ORDER BY name LIMIT 10");
                $items = $query->fetchAll(PDO::FETCH_ASSOC);

                foreach ($items as $item) {
                    $expandedUrl = str_replace('[slug]', $item['slug'], $url);
                    $expandedDescription = "Direct access to {$item['name']}";
                    fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                    $addedUrls++;
                }

            } elseif (strpos($url, '/activities/[slug]') !== false) {
                echo "Expanding activity URLs...\n";
                $query = $pdo->query("SELECT slug, name FROM activities WHERE published = 1 AND slug IS NOT NULL ORDER BY name LIMIT 10");
                $items = $query->fetchAll(PDO::FETCH_ASSOC);

                foreach ($items as $item) {
                    $expandedUrl = str_replace('[slug]', $item['slug'], $url);
                    $expandedDescription = "Direct access to {$item['name']}";
                    fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                    $addedUrls++;
                }

            } elseif (strpos($url, '/testimonials/[slug]') !== false) {
                echo "Expanding testimonial URLs...\n";
                $query = $pdo->query("SELECT slug, title, author FROM testimonials WHERE published = 1 AND slug IS NOT NULL ORDER BY author LIMIT 10");
                $items = $query->fetchAll(PDO::FETCH_ASSOC);

                foreach ($items as $item) {
                    $expandedUrl = str_replace('[slug]', $item['slug'], $url);
                    $expandedDescription = "Testimonial from {$item['author']}: {$item['title']}";
                    fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                    $addedUrls++;
                }

            } elseif (strpos($url, '/testimonial/[slug]') !== false) {
                echo "Expanding singular testimonial URLs...\n";
                $query = $pdo->query("SELECT slug, title, author FROM testimonials WHERE published = 1 AND slug IS NOT NULL ORDER BY author LIMIT 5");
                $items = $query->fetchAll(PDO::FETCH_ASSOC);

                foreach ($items as $item) {
                    $expandedUrl = str_replace('[slug]', $item['slug'], $url);
                    $expandedDescription = "Testimonial from {$item['author']}: {$item['title']} (singular URL)";
                    fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                    $addedUrls++;
                }

            } elseif (strpos($url, '/just_faqs/[id]') !== false) {
                echo "Expanding FAQ URLs...\n";
                $query = $pdo->query("SELECT id, question FROM faqs ORDER BY id LIMIT 10");
                $items = $query->fetchAll(PDO::FETCH_ASSOC);

                foreach ($items as $item) {
                    $expandedUrl = str_replace('[id]', $item['id'], $url);
                    $expandedDescription = "FAQ: " . substr($item['question'], 0, 50) . "...";
                    fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                    $addedUrls++;
                }

            } elseif (strpos($url, '/img/uploads/[id]_[version].[ext]') !== false) {
                echo "Expanding image URLs...\n";
                $query = $pdo->query("SELECT id, alt, extension FROM images WHERE published = 1 AND id IS NOT NULL ORDER BY id LIMIT 10");
                $items = $query->fetchAll(PDO::FETCH_ASSOC);

                foreach ($items as $item) {
                    $versions = ['thumb', 'medium', 'large'];

                    foreach ($versions as $version) {
                        $expandedUrl = str_replace(['[id]', '[version]', '[ext]'], [$item['id'], $version, $item['extension']], $url);
                        $expandedDescription = "Image: " . ($item['alt'] ?: "Image {$item['id']}") . " ({$version}.{$item['extension']})";
                        fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                        $addedUrls++;

                        // Limit to avoid too many image URLs
                        if ($addedUrls >= 30) break 2;
                    }
                }

            } elseif (strpos($url, '/search/[term]') !== false) {
                echo "Expanding search URLs...\n";
                $searchTerms = ['florida', 'california', 'new-york', 'texas', 'las-vegas', 'grand-canyon', 'yellowstone', 'disney', 'cruise', 'ranch'];

                foreach ($searchTerms as $term) {
                    $expandedUrl = str_replace('[term]', $term, $url);
                    $expandedDescription = "Search results for '{$term}'";
                    fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                    $addedUrls++;
                }

            } elseif (strpos($url, '/proxy/[action]') !== false) {
                echo "Expanding proxy URLs...\n";
                $proxyActions = ['geocode', 'weather', 'currency', 'translate', 'validate'];

                foreach ($proxyActions as $action) {
                    $expandedUrl = str_replace('[action]', $action, $url);
                    $expandedDescription = "Proxy endpoint for {$action} service";
                    fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                    $addedUrls++;
                }

            } elseif (strpos($url, '/v1/[endpoint]') !== false) {
                echo "Expanding legacy API URLs...\n";
                $apiEndpoints = ['geocode', 'maps', 'weather', 'destinations', 'search'];

                foreach ($apiEndpoints as $endpoint) {
                    $expandedUrl = str_replace('[endpoint]', $endpoint, $url);
                    $expandedDescription = "Legacy API endpoint for {$endpoint}";
                    fputcsv($expandedFile, [$expandedUrl, $urlType, $section, $expandedDescription, $status, 1]);
                    $addedUrls++;
                }
            }

        } else {
            // Keep existing non-placeholder URLs
            fputcsv($expandedFile, $urlRow);
        }
    }

    fclose($expandedFile);

    echo "\n=== PLACEHOLDER URL EXPANSION COMPLETE ===\n";
    echo "Placeholder patterns found: " . count($placeholderUrls) . "\n";
    echo "Actual URLs added: " . $addedUrls . "\n";
    echo "Original URL count: " . count($existingUrls) . "\n";
    echo "Expanded URL count: " . (count($existingUrls) - count($placeholderUrls) + $addedUrls) . "\n";
    echo "File created: output/complete_url_list_expanded.csv\n\n";

    echo "Placeholder patterns that were expanded:\n";
    foreach ($placeholderUrls as $placeholder) {
        echo "- {$placeholder[0]} ({$placeholder[5]} potential matches)\n";
    }

} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
