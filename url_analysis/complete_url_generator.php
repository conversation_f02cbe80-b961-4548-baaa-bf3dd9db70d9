<?php
/**
 * Complete URL List Generator
 * Generates a comprehensive list of all URLs in the Bon Voyage site
 * Including missing URLs identified by the user
 */

// Database configuration
$host = 'db';
$username = 'db';
$password = 'db';
$database = 'db';

// Domain prefix for all URLs
$domain = 'https://www.bon-voyage.co.uk';

// Output directory
$outputDir = 'output';
if (!is_dir($outputDir)) {
    mkdir($outputDir, 0755, true);
}

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Connected to database successfully.\n";
    echo "Generating complete URL list...\n\n";

    // Create complete URL list file
    $urlListFile = fopen($outputDir . '/complete_url_list.csv', 'w');
    fputcsv($urlListFile, [
        'URL',
        'URL Type',
        'Section',
        'Description',
        'Status',
        'Content Count'
    ]);

    $allUrls = [];

    // 1. STATIC URLS
    echo "Adding static URLs...\n";
    $staticUrls = [
        [$domain . '/', 'Static', 'Homepage', 'Site homepage', 'Active', 1],
        [$domain . '/mmenu', 'API', 'Navigation', 'Mobile menu API endpoint', 'Active', 1],
        [$domain . '/megamenu', 'API', 'Navigation', 'Desktop menu API endpoint', 'Active', 1],
        [$domain . '/cookies', 'Static', 'Legal', 'Cookie preferences page', 'Active', 1],
        [$domain . '/faqs', 'Static', 'Support', 'FAQ listing page', 'Active', 1],
        [$domain . '/search', 'Static', 'Search', 'Search results page', 'Active', 1],
        [$domain . '/start_planning_now', 'Static', 'Forms', 'Travel planning form', 'Active', 1],
        [$domain . '/make_an_enquiry', 'Static', 'Forms', 'Quick enquiry form', 'Active', 1],
        [$domain . '/social', 'Static', 'Forms', 'Social travel planning form', 'Active', 1],
        [$domain . '/page/testimonials', 'Static', 'Content', 'Testimonials listing', 'Active', 1],
        [$domain . '/page/the_bon_voyage_holiday_promise', 'Static', 'Content', 'Holiday promise page', 'Active', 1],
        [$domain . '/in_the_press', 'Static', 'Content', 'Press releases listing', 'Active', 1],
        [$domain . '/contact_us', 'Static', 'Forms', 'Contact us page', 'Active', 1],
        [$domain . '/subscriptions', 'Static', 'Account', 'Newsletter subscriptions', 'Active', 1],
    ];

    foreach ($staticUrls as $url) {
        fputcsv($urlListFile, $url);
        $allUrls[] = $url;
    }

    // 2. DESTINATIONS
    echo "Adding destination URLs...\n";
    $destinationsQuery = $pdo->query("SELECT slug, name FROM destinations WHERE published = 1 AND slug IS NOT NULL ORDER BY name");
    $destinations = $destinationsQuery->fetchAll(PDO::FETCH_ASSOC);

    // Main destinations listing
    fputcsv($urlListFile, [$domain . '/destinations', 'Listing', 'Destinations', 'All destinations listing', 'Active', count($destinations)]);
    $allUrls[] = [$domain . '/destinations', 'Listing', 'Destinations', 'All destinations listing', 'Active', count($destinations)];

    foreach ($destinations as $dest) {
        $urls = [
            [$domain . "/destinations/{$dest['slug']}", 'Dynamic', 'Destinations', $dest['name'], 'Active', 1],
            [$domain . "/destinations/{$dest['slug']}/accommodation", 'Dynamic', 'Destinations', "{$dest['name']} - Accommodation", 'Active', 1],
            [$domain . "/destinations/{$dest['slug']}/itineraries", 'Dynamic', 'Destinations', "{$dest['name']} - Itineraries", 'Active', 1],
            [$domain . "/destinations/{$dest['slug']}/activities", 'Dynamic', 'Destinations', "{$dest['name']} - Activities", 'Active', 1],
            [$domain . "/destinations/{$dest['slug']}/images", 'Dynamic', 'Destinations', "{$dest['name']} - Images", 'Active', 1],
            [$domain . "/destinations/{$dest['slug']}/videos", 'Dynamic', 'Destinations', "{$dest['name']} - Videos", 'Active', 1],
        ];

        foreach ($urls as $url) {
            fputcsv($urlListFile, $url);
            $allUrls[] = $url;
        }
    }

    // 3. HOLIDAY TYPES
    echo "Adding holiday type URLs...\n";
    $holidayTypesQuery = $pdo->query("SELECT slug, name FROM holiday_types WHERE published = 1 AND slug IS NOT NULL ORDER BY name");
    $holidayTypes = $holidayTypesQuery->fetchAll(PDO::FETCH_ASSOC);

    // Main holidays listing
    fputcsv($urlListFile, [$domain . '/holidays', 'Listing', 'Holidays', 'All holiday types listing', 'Active', count($holidayTypes)]);
    $allUrls[] = [$domain . '/holidays', 'Listing', 'Holidays', 'All holiday types listing', 'Active', count($holidayTypes)];

    foreach ($holidayTypes as $holiday) {
        $urls = [
            [$domain . "/holidays/{$holiday['slug']}", 'Dynamic', 'Holidays', $holiday['name'], 'Active', 1],
            [$domain . "/holidays/{$holiday['slug']}/destinations", 'Dynamic', 'Holidays', "{$holiday['name']} - Destinations", 'Active', 1],
            [$domain . "/holidays/{$holiday['slug']}/accommodation", 'Dynamic', 'Holidays', "{$holiday['name']} - Accommodation", 'Active', 1],
            [$domain . "/holidays/{$holiday['slug']}/itineraries", 'Dynamic', 'Holidays', "{$holiday['name']} - Itineraries", 'Active', 1],
            [$domain . "/holidays/{$holiday['slug']}/activities", 'Dynamic', 'Holidays', "{$holiday['name']} - Activities", 'Active', 1],
        ];

        foreach ($urls as $url) {
            fputcsv($urlListFile, $url);
            $allUrls[] = $url;
        }
    }

    // 4. CANONICAL CONTENT URLS
    echo "Adding canonical content URLs...\n";

    // Accommodations
    $accommodationsQuery = $pdo->query("SELECT COUNT(*) as count FROM accommodation WHERE published = 1 AND slug IS NOT NULL");
    $accommodationCount = $accommodationsQuery->fetch(PDO::FETCH_ASSOC)['count'];
    fputcsv($urlListFile, [$domain . '/accommodations/[slug]', 'Canonical', 'Accommodation', 'Direct accommodation access', 'Active', $accommodationCount]);
    $allUrls[] = [$domain . '/accommodations/[slug]', 'Canonical', 'Accommodation', 'Direct accommodation access', 'Active', $accommodationCount];

    // Itineraries
    $itinerariesQuery = $pdo->query("SELECT COUNT(*) as count FROM itineraries WHERE published = 1 AND slug IS NOT NULL");
    $itineraryCount = $itinerariesQuery->fetch(PDO::FETCH_ASSOC)['count'];
    fputcsv($urlListFile, [$domain . '/itineraries/[slug]', 'Canonical', 'Itineraries', 'Direct itinerary access', 'Active', $itineraryCount]);
    $allUrls[] = [$domain . '/itineraries/[slug]', 'Canonical', 'Itineraries', 'Direct itinerary access', 'Active', $itineraryCount];

    // Activities
    $activitiesQuery = $pdo->query("SELECT COUNT(*) as count FROM activities WHERE published = 1 AND slug IS NOT NULL");
    $activityCount = $activitiesQuery->fetch(PDO::FETCH_ASSOC)['count'];
    fputcsv($urlListFile, [$domain . '/activities/[slug]', 'Canonical', 'Activities', 'Direct activity access', 'Active', $activityCount]);
    $allUrls[] = [$domain . '/activities/[slug]', 'Canonical', 'Activities', 'Direct activity access', 'Active', $activityCount];

    // Testimonials
    $testimonialsQuery = $pdo->query("SELECT COUNT(*) as count FROM testimonials WHERE published = 1 AND slug IS NOT NULL");
    $testimonialCount = $testimonialsQuery->fetch(PDO::FETCH_ASSOC)['count'];
    fputcsv($urlListFile, [$domain . '/testimonials/[slug]', 'Canonical', 'Testimonials', 'Direct testimonial access', 'Active', $testimonialCount]);
    fputcsv($urlListFile, [$domain . '/testimonial/[slug]', 'Canonical', 'Testimonials', 'Alternative testimonial access', 'Active', $testimonialCount]);
    $allUrls[] = [$domain . '/testimonials/[slug]', 'Canonical', 'Testimonials', 'Direct testimonial access', 'Active', $testimonialCount];
    $allUrls[] = [$domain . '/testimonial/[slug]', 'Canonical', 'Testimonials', 'Alternative testimonial access', 'Active', $testimonialCount];

    // 5. CAMPAIGNS/LANDING PAGES
    echo "Adding campaign URLs...\n";
    $landingPagesQuery = $pdo->query("SELECT slug, name FROM landing_pages WHERE published = 1 AND slug IS NOT NULL ORDER BY name");
    $landingPages = $landingPagesQuery->fetchAll(PDO::FETCH_ASSOC);

    // Main campaigns listing
    fputcsv($urlListFile, [$domain . '/campaigns', 'Listing', 'Campaigns', 'All campaigns listing', 'Active', count($landingPages)]);
    $allUrls[] = [$domain . '/campaigns', 'Listing', 'Campaigns', 'All campaigns listing', 'Active', count($landingPages)];

    foreach ($landingPages as $landing) {
        $urls = [
            [$domain . "/campaigns/{$landing['slug']}", 'Dynamic', 'Campaigns', $landing['name'], 'Active', 1],
            [$domain . "/campaigns/{$landing['slug']}/destinations", 'Dynamic', 'Campaigns', "{$landing['name']} - Destinations", 'Active', 1],
            [$domain . "/campaigns/{$landing['slug']}/accommodation", 'Dynamic', 'Campaigns', "{$landing['name']} - Accommodation", 'Active', 1],
            [$domain . "/campaigns/{$landing['slug']}/itineraries", 'Dynamic', 'Campaigns', "{$landing['name']} - Itineraries", 'Active', 1],
        ];

        foreach ($urls as $url) {
            fputcsv($urlListFile, $url);
            $allUrls[] = $url;
        }
    }

    // 6. STATIC PAGES
    echo "Adding static page URLs...\n";
    $pagesQuery = $pdo->query("SELECT slug, navigation_label FROM pages WHERE published = 1 AND slug IS NOT NULL ORDER BY navigation_label");
    $pages = $pagesQuery->fetchAll(PDO::FETCH_ASSOC);

    foreach ($pages as $page) {
        $url = [$domain . "/page/{$page['slug']}", 'Dynamic', 'Pages', $page['navigation_label'] ?: 'Static Page', 'Active', 1];
        fputcsv($urlListFile, $url);
        $allUrls[] = $url;
    }

    // 7. MISSING URLS IDENTIFIED BY USER
    echo "Adding missing URLs identified by user...\n";

    // Spotlights
    $spotlightsQuery = $pdo->query("SELECT slug, name FROM spotlights WHERE published = 1 AND slug IS NOT NULL ORDER BY name");
    $spotlights = $spotlightsQuery->fetchAll(PDO::FETCH_ASSOC);

    // Add spotlight listings
    fputcsv($urlListFile, [$domain . '/whats_hot', 'Listing', 'Content', 'What\'s Hot listing', 'Active', count($spotlights)]);
    fputcsv($urlListFile, [$domain . '/spotlights', 'Listing', 'Content', 'Spotlights listing', 'Active', count($spotlights)]);
    $allUrls[] = [$domain . '/whats_hot', 'Listing', 'Content', 'What\'s Hot listing', 'Active', count($spotlights)];
    $allUrls[] = [$domain . '/spotlights', 'Listing', 'Content', 'Spotlights listing', 'Active', count($spotlights)];

    foreach ($spotlights as $spotlight) {
        $urls = [
            [$domain . "/whats_hot/{$spotlight['slug']}", 'Dynamic', 'Content', $spotlight['name'], 'Active', 1],
            [$domain . "/spotlight/{$spotlight['slug']}", 'Dynamic', 'Content', $spotlight['name'], 'Active', 1],
            [$domain . "/spotlights/{$spotlight['slug']}", 'Dynamic', 'Content', $spotlight['name'], 'Active', 1],
        ];

        foreach ($urls as $url) {
            fputcsv($urlListFile, $url);
            $allUrls[] = $url;
        }
    }

    // FAQ individual pages
    $faqsQuery = $pdo->query("SELECT COUNT(*) as count FROM faqs");
    $faqCount = $faqsQuery->fetch(PDO::FETCH_ASSOC)['count'];
    fputcsv($urlListFile, [$domain . '/just_faqs/[id]', 'Dynamic', 'Support', 'Individual FAQ pages', 'Active', $faqCount]);
    $allUrls[] = [$domain . '/just_faqs/[id]', 'Dynamic', 'Support', 'Individual FAQ pages', 'Active', $faqCount];

    // 8. API AND SYSTEM URLS
    echo "Adding API and system URLs...\n";
    $systemUrls = [
        [$domain . '/api/navigation/megamenu', 'API', 'Navigation', 'Desktop navigation API', 'Active', 1],
        [$domain . '/api/navigation/mmenu', 'API', 'Navigation', 'Mobile navigation API', 'Active', 1],
        [$domain . '/img/uploads/[id]_[version].[ext]', 'System', 'Images', 'Image serving endpoint', 'Active', 16203],
        [$domain . '/search/[term]', 'Dynamic', 'Search', 'Search with specific term', 'Active', 1],
        [$domain . '/webadmin', 'Admin', 'System', 'Admin panel access', 'Active', 1],
        [$domain . '/proxy/[action]', 'API', 'System', 'Proxy endpoints', 'Active', 1],
        [$domain . '/v1/[endpoint]', 'API', 'System', 'Legacy API endpoints', 'Active', 1],
    ];

    foreach ($systemUrls as $url) {
        fputcsv($urlListFile, $url);
        $allUrls[] = $url;
    }

    fclose($urlListFile);

    echo "\n=== COMPLETE URL LIST GENERATED ===\n";
    echo "Total URLs: " . count($allUrls) . "\n";
    echo "File created: output/complete_url_list.csv\n\n";

    // Generate summary statistics
    $urlTypes = [];
    $sections = [];
    foreach ($allUrls as $url) {
        $type = $url[1];
        $section = $url[2];
        $urlTypes[$type] = ($urlTypes[$type] ?? 0) + 1;
        $sections[$section] = ($sections[$section] ?? 0) + 1;
    }

    echo "URL Types:\n";
    arsort($urlTypes);
    foreach ($urlTypes as $type => $count) {
        echo "  $type: $count URLs\n";
    }

    echo "\nSections:\n";
    arsort($sections);
    foreach ($sections as $section => $count) {
        echo "  $section: $count URLs\n";
    }

} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
