"Content Type","Primary Route","Duplicate Routes","Content Count",Description,Recommendation
"Spotlights/What's Hot",/whats_hot,"/spotlight, /spotlights",28,"Three different URL patterns serving the same spotlight content","Standardize on /whats_hot, redirect others"
"Individual Spotlights",/whats_hot/:slug,"/spotlight/:slug, /spotlights/:slug",28,"Three URL patterns for individual spotlight pages","Standardize on /whats_hot/:slug, redirect others"
"Individual Testimonials",/testimonials/:slug,/testimonial/:slug,105,"Singular vs plural URL patterns for testimonials","Standardize on /testimonials/:slug (plural), redirect singular"
"Destination Pages",/destinations/:destination_slug,"/holidays/:holiday_slug/destinations/:destination_slug, /campaigns/:campaign_slug/destinations/:destination_slug",429,"Same destination content accessible via different section contexts","Keep contextual URLs for navigation, ensure canonical URLs point to /destinations/:slug"
"Accommodation Pages",/accommodations/:accommodation_slug,"/destinations/:destination_slug/accommodation/:accommodation_slug, /holidays/:holiday_slug/accommodation/:accommodation_slug, /campaigns/:campaign_slug/accommodation/:accommodation_slug",665,"Same accommodation content accessible via multiple section contexts","Keep contextual URLs for navigation, ensure canonical URLs point to /accommodations/:slug"
"Itinerary Pages",/itineraries/:itinerary_slug,"/destinations/:destination_slug/itinerary/:itinerary_slug, /holidays/:holiday_slug/itinerary/:itinerary_slug, /campaigns/:campaign_slug/itinerary/:itinerary_slug",250,"Same itinerary content accessible via multiple section contexts","Keep contextual URLs for navigation, ensure canonical URLs point to /itineraries/:slug"
"Activity Pages",/activities/:activity_slug,"/destinations/:destination_slug/activity/:activity_slug, /holidays/:holiday_slug/activity/:activity_slug",565,"Same activity content accessible via multiple section contexts","Keep contextual URLs for navigation, ensure canonical URLs point to /activities/:slug"
"Navigation APIs","/mmenu, /megamenu","/api/navigation/mmenu, /api/navigation/megamenu",2,"Navigation APIs accessible via both root and /api/ paths","Standardize on /api/navigation/ prefix for consistency"
"Legacy URL Patterns","Current URL structure","Legacy patterns (from .htaccess redirects)",1820,"1,820 redirect rules suggest many legacy URL patterns that may still be accessible","Audit redirect rules to ensure no legacy URLs are still accessible without redirects"
