<?php
/**
 * URL Analysis Runner
 * Runs all URL analysis scripts and generates complete output
 */

echo "=== Bon Voyage URL Analysis Runner ===\n\n";

// Check if we're in the right directory
if (!file_exists('url_structure_analyzer.php')) {
    die("Error: Please run this script from the url_analysis directory\n");
}

// Create output directory if it doesn't exist
if (!is_dir('output')) {
    mkdir('output', 0755, true);
    echo "Created output directory\n";
}

echo "Running URL structure analysis...\n";
echo "=====================================\n";

// Run main URL analysis
$output = [];
$returnCode = 0;
exec('php url_structure_analyzer.php 2>&1', $output, $returnCode);

foreach ($output as $line) {
    echo $line . "\n";
}

if ($returnCode !== 0) {
    echo "\nError: URL structure analysis failed with code $returnCode\n";
    exit(1);
}

echo "\n\nRunning complete URL list generation...\n";
echo "=======================================\n";

// Run complete URL generator
$output = [];
$returnCode = 0;
exec('php complete_url_generator.php 2>&1', $output, $returnCode);

foreach ($output as $line) {
    echo $line . "\n";
}

if ($returnCode !== 0) {
    echo "\nError: Complete URL generation failed with code $returnCode\n";
    exit(1);
}

echo "\n\nRunning duplicate routes analysis...\n";
echo "====================================\n";

// Run duplicate routes analyzer
$output = [];
$returnCode = 0;
exec('php duplicate_routes_analyzer.php 2>&1', $output, $returnCode);

foreach ($output as $line) {
    echo $line . "\n";
}

if ($returnCode !== 0) {
    echo "\nError: Duplicate routes analysis failed with code $returnCode\n";
    exit(1);
}

echo "\n\nRunning .htaccess redirect analysis...\n";
echo "======================================\n";

// Run redirect analysis
$output = [];
$returnCode = 0;
exec('cd scripts && php htaccess_redirect_analyzer.php 2>&1', $output, $returnCode);

foreach ($output as $line) {
    echo $line . "\n";
}

if ($returnCode !== 0) {
    echo "\nError: .htaccess redirect analysis failed with code $returnCode\n";
    exit(1);
}

echo "\n\n=== Analysis Complete ===\n";
echo "All files generated in the 'output/' directory:\n";

// List generated files
$files = glob('output/*.csv');
foreach ($files as $file) {
    $size = filesize($file);
    $sizeFormatted = $size > 1024 ? round($size/1024, 1) . 'KB' : $size . 'B';
    echo "- " . basename($file) . " ($sizeFormatted)\n";
}

echo "\nTotal output directory size: ";
$totalSize = 0;
foreach ($files as $file) {
    $totalSize += filesize($file);
}
echo $totalSize > 1024 ? round($totalSize/1024, 1) . 'KB' : $totalSize . 'B';
echo "\n";
