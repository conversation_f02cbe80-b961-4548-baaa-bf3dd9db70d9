# Complete URL List - Bon Voyage Website

This document provides a comprehensive overview of all URLs in the Bon Voyage website, including the previously missing URLs identified. **All URLs include the full domain prefix: `https://www.bon-voyage.co.uk`**

## 📊 Summary Statistics

- **Total URLs**: 3,033 (expanded from 2,967)
- **Domain**: `https://www.bon-voyage.co.uk`
- **Content Types**: 7 different URL types
- **Sections**: 18 different content sections
- **File**: `output/complete_url_list.csv`
- **Placeholder Expansion**: +66 actual URLs from database entries

## 🌐 URL Types Breakdown

| URL Type | Count | Description |
|----------|-------|-------------|
| **Dynamic** | 2,937 | Parameter-based URLs with slugs |
| **Static** | 12 | Fixed URLs for core pages |
| **API** | 6 | Data endpoints and services |
| **Listing** | 5 | Index/overview pages |
| **Canonical** | 5 | Direct content access patterns |
| **System** | 1 | Image serving and utilities |
| **Admin** | 1 | Backend administration |

## 📂 Content Sections Breakdown

| Section | Count | Description |
|---------|-------|-------------|
| **Destinations** | 2,575 | Travel destinations and related content |
| **Campaigns** | 153 | Marketing campaigns and landing pages |
| **Content** | 89 | Spotlights, press releases, and editorial |
| **Holidays** | 81 | Holiday types and related content |
| **Pages** | 45 | Static content pages |
| **Navigation** | 4 | Menu and navigation APIs |
| **Forms** | 4 | Contact, enquiry, and planning forms |
| **System** | 3 | Technical and API endpoints |
| **Search** | 2 | Search functionality |
| **Testimonials** | 2 | Customer reviews and testimonials |
| **Support** | 2 | FAQ and help content |
| **Account** | 1 | User account management |
| **Accommodation** | 1 | Hotel and lodging content |
| **Itineraries** | 1 | Tour packages and trips |
| **Activities** | 1 | Tours and experiences |
| **Legal** | 1 | Cookie and privacy pages |
| **Images** | 1 | Media serving |
| **Homepage** | 1 | Site homepage |

## ✅ Previously Missing URLs (Now Included)

The following URLs were identified as missing and have been added to the complete list:

### Spotlights/What's Hot Content
- `https://www.bon-voyage.co.uk/whats_hot` - What's Hot listing (28 items)
- `https://www.bon-voyage.co.uk/whats_hot/:slug` - Individual What's Hot pages
- `https://www.bon-voyage.co.uk/spotlight/:slug` - Individual spotlight pages
- `https://www.bon-voyage.co.uk/spotlights` - Spotlights listing
- `https://www.bon-voyage.co.uk/spotlights/:slug` - Individual spotlight pages

### FAQ Content
- `https://www.bon-voyage.co.uk/just_faqs/:id` - Individual FAQ pages (26 items)

### Additional Static Pages
- `https://www.bon-voyage.co.uk/contact_us` - Contact us page
- `https://www.bon-voyage.co.uk/subscriptions` - Newsletter subscriptions

## 🔍 Key URL Patterns

### Destinations (2,575 URLs)
- `https://www.bon-voyage.co.uk/destinations` - Main listing
- `https://www.bon-voyage.co.uk/destinations/:slug` - Individual destinations (429)
- `https://www.bon-voyage.co.uk/destinations/:slug/accommodation` - Destination hotels
- `https://www.bon-voyage.co.uk/destinations/:slug/itineraries` - Destination tours
- `https://www.bon-voyage.co.uk/destinations/:slug/activities` - Destination activities
- `https://www.bon-voyage.co.uk/destinations/:slug/images` - Destination galleries
- `https://www.bon-voyage.co.uk/destinations/:slug/videos` - Destination videos

### Holiday Types (81 URLs)
- `/holidays` - Main listing
- `/holidays/:slug` - Individual holiday types (16)
- `/holidays/:slug/destinations` - Holiday destinations
- `/holidays/:slug/accommodation` - Holiday accommodation
- `/holidays/:slug/itineraries` - Holiday itineraries
- `/holidays/:slug/activities` - Holiday activities

### Campaigns (153 URLs)
- `/campaigns` - Main listing
- `/campaigns/:slug` - Individual campaigns (38)
- `/campaigns/:slug/destinations` - Campaign destinations
- `/campaigns/:slug/accommodation` - Campaign accommodation
- `/campaigns/:slug/itineraries` - Campaign itineraries

### Content (89 URLs)
- `/whats_hot` - What's Hot listing
- `/whats_hot/:slug` - Individual What's Hot items (28)
- `/spotlight/:slug` - Alternative spotlight URLs (28)
- `/spotlights` - Spotlights listing
- `/spotlights/:slug` - Alternative spotlight URLs (28)
- `/page/testimonials` - Testimonials listing
- `/in_the_press` - Press releases listing

### Canonical Access (5 patterns)
- `/accommodations/:slug` - Direct hotel access (665 items)
- `/itineraries/:slug` - Direct tour access (250 items)
- `/activities/:slug` - Direct activity access (565 items)
- `/testimonials/:slug` - Direct testimonial access (105 items)
- `/testimonial/:slug` - Alternative testimonial access (105 items)

## 🛠️ Technical URLs

### API Endpoints
- `/mmenu` - Mobile menu API
- `/megamenu` - Desktop menu API
- `/api/navigation/megamenu` - Navigation API (desktop)
- `/api/navigation/mmenu` - Navigation API (mobile)
- `/proxy/:action` - Proxy endpoints
- `/v1/:endpoint` - Legacy API endpoints

### System URLs
- `/img/uploads/:id_:version.:extension` - Image serving (16,203 images)
- `/search/:term` - Search with specific terms
- `/webadmin` - Admin panel access

## 📋 Usage Notes

### For SEO Analysis
- Use the complete list to audit all site URLs
- Identify high-traffic patterns for optimization
- Plan URL structure improvements

### For Development
- Reference for routing configuration
- URL pattern validation
- Migration planning support

### For Content Management
- Complete inventory of all content URLs
- Content organization and categorization
- Editorial workflow planning

## 🔄 Regenerating the List

To regenerate the complete URL list:

```bash
# From the bon-voyage root directory
ddev exec "cd url_analysis && php complete_url_generator.php"
```

The generator will:
1. Connect to the database
2. Query all content types and their slugs
3. Generate URLs for all patterns
4. Include the previously missing URLs
5. Output statistics and create `output/complete_url_list.csv`

---

*Generated on: September 3, 2025*  
*Total URLs: 2,967*  
*File size: ~400KB*
