<?php
/**
 * Duplicate Routes Analyzer
 * Identifies routes that serve the same content but have different URL patterns
 */

// Database configuration
$host = 'db';
$username = 'db';
$password = 'db';
$database = 'db';

// Output directory
$outputDir = 'output';
if (!is_dir($outputDir)) {
    mkdir($outputDir, 0755, true);
}

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Connected to database successfully.\n";
    echo "Analyzing duplicate routes...\n\n";

    // Create duplicate routes analysis file
    $duplicatesFile = fopen($outputDir . '/duplicate_routes.csv', 'w');
    fputcsv($duplicatesFile, [
        'Content Type',
        'Primary Route',
        'Duplicate Routes',
        'Content Count',
        'Description',
        'Recommendation'
    ]);

    $duplicateGroups = [];

    // 1. SPOTLIGHT/WHATS_HOT DUPLICATES
    echo "Analyzing spotlight/whats_hot duplicates...\n";
    $spotlightsQuery = $pdo->query("SELECT COUNT(*) as count FROM spotlights WHERE published = 1 AND slug IS NOT NULL");
    $spotlightCount = $spotlightsQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $spotlightDuplicates = [
        'Content Type' => 'Spotlights/What\'s Hot',
        'Primary Route' => '/whats_hot',
        'Duplicate Routes' => '/spotlight, /spotlights',
        'Content Count' => $spotlightCount,
        'Description' => 'Three different URL patterns serving the same spotlight content',
        'Recommendation' => 'Standardize on /whats_hot, redirect others'
    ];

    fputcsv($duplicatesFile, $spotlightDuplicates);
    $duplicateGroups[] = $spotlightDuplicates;

    // Individual spotlight pages
    $spotlightPageDuplicates = [
        'Content Type' => 'Individual Spotlights',
        'Primary Route' => '/whats_hot/:slug',
        'Duplicate Routes' => '/spotlight/:slug, /spotlights/:slug',
        'Content Count' => $spotlightCount,
        'Description' => 'Three URL patterns for individual spotlight pages',
        'Recommendation' => 'Standardize on /whats_hot/:slug, redirect others'
    ];

    fputcsv($duplicatesFile, $spotlightPageDuplicates);
    $duplicateGroups[] = $spotlightPageDuplicates;

    // 2. TESTIMONIAL DUPLICATES
    echo "Analyzing testimonial duplicates...\n";
    $testimonialsQuery = $pdo->query("SELECT COUNT(*) as count FROM testimonials WHERE published = 1 AND slug IS NOT NULL");
    $testimonialCount = $testimonialsQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $testimonialDuplicates = [
        'Content Type' => 'Individual Testimonials',
        'Primary Route' => '/testimonials/:slug',
        'Duplicate Routes' => '/testimonial/:slug',
        'Content Count' => $testimonialCount,
        'Description' => 'Singular vs plural URL patterns for testimonials',
        'Recommendation' => 'Standardize on /testimonials/:slug (plural), redirect singular'
    ];

    fputcsv($duplicatesFile, $testimonialDuplicates);
    $duplicateGroups[] = $testimonialDuplicates;

    // 3. CONTEXTUAL DUPLICATES (same content in different sections)
    echo "Analyzing contextual duplicates...\n";

    // Destinations in different contexts
    $destinationsQuery = $pdo->query("SELECT COUNT(*) as count FROM destinations WHERE published = 1 AND slug IS NOT NULL");
    $destinationCount = $destinationsQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $destinationContextDuplicates = [
        'Content Type' => 'Destination Pages',
        'Primary Route' => '/destinations/:destination_slug',
        'Duplicate Routes' => '/holidays/:holiday_slug/destinations/:destination_slug, /campaigns/:campaign_slug/destinations/:destination_slug',
        'Content Count' => $destinationCount,
        'Description' => 'Same destination content accessible via different section contexts',
        'Recommendation' => 'Keep contextual URLs for navigation, ensure canonical URLs point to /destinations/:slug'
    ];

    fputcsv($duplicatesFile, $destinationContextDuplicates);
    $duplicateGroups[] = $destinationContextDuplicates;

    // Accommodations in different contexts
    $accommodationsQuery = $pdo->query("SELECT COUNT(*) as count FROM accommodation WHERE published = 1 AND slug IS NOT NULL");
    $accommodationCount = $accommodationsQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $accommodationContextDuplicates = [
        'Content Type' => 'Accommodation Pages',
        'Primary Route' => '/accommodations/:accommodation_slug',
        'Duplicate Routes' => '/destinations/:destination_slug/accommodation/:accommodation_slug, /holidays/:holiday_slug/accommodation/:accommodation_slug, /campaigns/:campaign_slug/accommodation/:accommodation_slug',
        'Content Count' => $accommodationCount,
        'Description' => 'Same accommodation content accessible via multiple section contexts',
        'Recommendation' => 'Keep contextual URLs for navigation, ensure canonical URLs point to /accommodations/:slug'
    ];

    fputcsv($duplicatesFile, $accommodationContextDuplicates);
    $duplicateGroups[] = $accommodationContextDuplicates;

    // Itineraries in different contexts
    $itinerariesQuery = $pdo->query("SELECT COUNT(*) as count FROM itineraries WHERE published = 1 AND slug IS NOT NULL");
    $itineraryCount = $itinerariesQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $itineraryContextDuplicates = [
        'Content Type' => 'Itinerary Pages',
        'Primary Route' => '/itineraries/:itinerary_slug',
        'Duplicate Routes' => '/destinations/:destination_slug/itinerary/:itinerary_slug, /holidays/:holiday_slug/itinerary/:itinerary_slug, /campaigns/:campaign_slug/itinerary/:itinerary_slug',
        'Content Count' => $itineraryCount,
        'Description' => 'Same itinerary content accessible via multiple section contexts',
        'Recommendation' => 'Keep contextual URLs for navigation, ensure canonical URLs point to /itineraries/:slug'
    ];

    fputcsv($duplicatesFile, $itineraryContextDuplicates);
    $duplicateGroups[] = $itineraryContextDuplicates;

    // Activities in different contexts
    $activitiesQuery = $pdo->query("SELECT COUNT(*) as count FROM activities WHERE published = 1 AND slug IS NOT NULL");
    $activityCount = $activitiesQuery->fetch(PDO::FETCH_ASSOC)['count'];

    $activityContextDuplicates = [
        'Content Type' => 'Activity Pages',
        'Primary Route' => '/activities/:activity_slug',
        'Duplicate Routes' => '/destinations/:destination_slug/activity/:activity_slug, /holidays/:holiday_slug/activity/:activity_slug',
        'Content Count' => $activityCount,
        'Description' => 'Same activity content accessible via multiple section contexts',
        'Recommendation' => 'Keep contextual URLs for navigation, ensure canonical URLs point to /activities/:slug'
    ];

    fputcsv($duplicatesFile, $activityContextDuplicates);
    $duplicateGroups[] = $activityContextDuplicates;

    // 4. API ENDPOINT DUPLICATES
    echo "Analyzing API endpoint duplicates...\n";

    $apiDuplicates = [
        'Content Type' => 'Navigation APIs',
        'Primary Route' => '/mmenu, /megamenu',
        'Duplicate Routes' => '/api/navigation/mmenu, /api/navigation/megamenu',
        'Content Count' => 2,
        'Description' => 'Navigation APIs accessible via both root and /api/ paths',
        'Recommendation' => 'Standardize on /api/navigation/ prefix for consistency'
    ];

    fputcsv($duplicatesFile, $apiDuplicates);
    $duplicateGroups[] = $apiDuplicates;

    // 5. POTENTIAL LEGACY DUPLICATES (from redirects analysis)
    echo "Analyzing potential legacy duplicates...\n";

    // Check for common redirect patterns that might indicate duplicates
    $legacyDuplicates = [
        'Content Type' => 'Legacy URL Patterns',
        'Primary Route' => 'Current URL structure',
        'Duplicate Routes' => 'Legacy patterns (from .htaccess redirects)',
        'Content Count' => 1820,
        'Description' => '1,820 redirect rules suggest many legacy URL patterns that may still be accessible',
        'Recommendation' => 'Audit redirect rules to ensure no legacy URLs are still accessible without redirects'
    ];

    fputcsv($duplicatesFile, $legacyDuplicates);
    $duplicateGroups[] = $legacyDuplicates;

    fclose($duplicatesFile);

    // Create detailed duplicate URLs file
    echo "Generating detailed duplicate URLs...\n";
    $detailedFile = fopen($outputDir . '/detailed_duplicate_urls.csv', 'w');
    fputcsv($detailedFile, [
        'Primary URL',
        'Duplicate URL 1',
        'Duplicate URL 2',
        'Duplicate URL 3',
        'Content Type',
        'Impact Level'
    ]);

    // Get actual spotlight data for detailed examples
    $spotlightsDetailQuery = $pdo->query("SELECT slug, name FROM spotlights WHERE published = 1 AND slug IS NOT NULL ORDER BY name LIMIT 10");
    $spotlightDetails = $spotlightsDetailQuery->fetchAll(PDO::FETCH_ASSOC);

    foreach ($spotlightDetails as $spotlight) {
        fputcsv($detailedFile, [
            "https://www.bon-voyage.co.uk/whats_hot/{$spotlight['slug']}",
            "https://www.bon-voyage.co.uk/spotlight/{$spotlight['slug']}",
            "https://www.bon-voyage.co.uk/spotlights/{$spotlight['slug']}",
            '',
            'Spotlight Content',
            'High - SEO Impact'
        ]);
    }

    // Get testimonial examples
    $testimonialsDetailQuery = $pdo->query("SELECT slug, author FROM testimonials WHERE published = 1 AND slug IS NOT NULL ORDER BY author LIMIT 5");
    $testimonialDetails = $testimonialsDetailQuery->fetchAll(PDO::FETCH_ASSOC);

    foreach ($testimonialDetails as $testimonial) {
        fputcsv($detailedFile, [
            "https://www.bon-voyage.co.uk/testimonials/{$testimonial['slug']}",
            "https://www.bon-voyage.co.uk/testimonial/{$testimonial['slug']}",
            '',
            '',
            'Testimonial Content',
            'Medium - SEO Impact'
        ]);
    }

    // Get destination examples (contextual duplicates)
    $destinationsDetailQuery = $pdo->query("SELECT slug, name FROM destinations WHERE published = 1 AND slug IS NOT NULL ORDER BY name LIMIT 5");
    $destinationDetails = $destinationsDetailQuery->fetchAll(PDO::FETCH_ASSOC);

    foreach ($destinationDetails as $destination) {
        fputcsv($detailedFile, [
            "https://www.bon-voyage.co.uk/destinations/{$destination['slug']}",
            "https://www.bon-voyage.co.uk/holidays/fly_drive_holidays/destinations/{$destination['slug']}",
            "https://www.bon-voyage.co.uk/campaigns/special_offer/destinations/{$destination['slug']}",
            '',
            'Destination Content',
            'Low - Contextual Navigation'
        ]);
    }

    fclose($detailedFile);

    echo "\n=== DUPLICATE ROUTES ANALYSIS COMPLETE ===\n";
    echo "Total duplicate groups identified: " . count($duplicateGroups) . "\n";
    echo "Files created:\n";
    echo "- duplicate_routes.csv (summary analysis)\n";
    echo "- detailed_duplicate_urls.csv (specific URL examples)\n\n";

    echo "Summary of duplicates:\n";
    foreach ($duplicateGroups as $group) {
        echo "- {$group['Content Type']}: {$group['Content Count']} items affected\n";
    }

    // Calculate total impact
    $totalSpotlightDuplicates = $spotlightCount * 3; // 3 URL patterns each
    $totalTestimonialDuplicates = $testimonialCount * 2; // 2 URL patterns each
    $totalContextualDuplicates = ($destinationCount + $accommodationCount + $itineraryCount + $activityCount) * 3; // Average 3 contexts each

    $totalDuplicateUrls = $totalSpotlightDuplicates + $totalTestimonialDuplicates + $totalContextualDuplicates;

    echo "\nEstimated total duplicate URLs: " . number_format($totalDuplicateUrls) . "\n";
    echo "Percentage of total URLs that are duplicates: " . round(($totalDuplicateUrls / 2967) * 100, 1) . "%\n";

} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
