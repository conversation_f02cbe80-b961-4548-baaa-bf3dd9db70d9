<?php
/**
 * Test script to verify noindex implementation
 * Tests the URLs that should have noindex meta tags
 */

echo "Testing noindex implementation for Bon Voyage website...\n\n";

// URLs to test
$testUrls = [
    // Testimonial pages (should have noindex)
    'https://www.bon-voyage.co.uk/testimonials/sian_is_incredible_july_2025',
    'https://www.bon-voyage.co.uk/testimonials/rachel_at_bon_voyage_literally_arranged_my_dream_holiday_april_2023',
    'https://www.bon-voyage.co.uk/testimonial/loved_our_holiday_and_would_highly_recommend_bon_voyage_may_2024',
    
    // Subscriptions page (should have noindex)
    'https://www.bon-voyage.co.uk/subscriptions',
    
    // Control URLs (should NOT have noindex)
    'https://www.bon-voyage.co.uk/',
    'https://www.bon-voyage.co.uk/destinations/usa_holidays',
    'https://www.bon-voyage.co.uk/testimonials', // Index page should be indexable
];

function checkNoIndex($url) {
    echo "Testing: $url\n";
    
    // Get the HTML content
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (compatible; NoindexTester/1.0)'
        ]
    ]);
    
    $html = @file_get_contents($url, false, $context);
    
    if ($html === false) {
        echo "  ❌ Could not fetch URL\n\n";
        return false;
    }
    
    // Check for noindex meta tag
    $hasNoIndex = false;
    $hasNoFollow = false;
    
    // Look for robots meta tag with noindex
    if (preg_match('/<meta\s+name=["\']robots["\']\s+content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
        $robotsContent = strtolower($matches[1]);
        $hasNoIndex = strpos($robotsContent, 'noindex') !== false;
        $hasNoFollow = strpos($robotsContent, 'nofollow') !== false;
        
        echo "  🤖 Robots meta tag found: " . $matches[1] . "\n";
    } else {
        echo "  ℹ️  No robots meta tag found\n";
    }
    
    // Check HTTP headers for X-Robots-Tag
    $headers = get_headers($url, 1);
    if (isset($headers['X-Robots-Tag'])) {
        echo "  📡 X-Robots-Tag header: " . $headers['X-Robots-Tag'] . "\n";
        $headerContent = strtolower($headers['X-Robots-Tag']);
        $hasNoIndex = $hasNoIndex || strpos($headerContent, 'noindex') !== false;
        $hasNoFollow = $hasNoFollow || strpos($headerContent, 'nofollow') !== false;
    }
    
    // Determine expected behavior
    $shouldHaveNoIndex = (
        strpos($url, '/testimonials/') !== false ||  // Individual testimonial pages
        strpos($url, '/testimonial/') !== false ||   // Alternative testimonial URLs
        strpos($url, '/subscriptions') !== false     // Subscriptions page
    );
    
    // Report results
    if ($shouldHaveNoIndex) {
        if ($hasNoIndex) {
            echo "  ✅ CORRECT: Page has noindex (as expected)\n";
        } else {
            echo "  ❌ ERROR: Page should have noindex but doesn't\n";
        }
    } else {
        if ($hasNoIndex) {
            echo "  ⚠️  WARNING: Page has noindex but shouldn't\n";
        } else {
            echo "  ✅ CORRECT: Page is indexable (as expected)\n";
        }
    }
    
    echo "\n";
    return $hasNoIndex;
}

// Test all URLs
$results = [];
foreach ($testUrls as $url) {
    $results[$url] = checkNoIndex($url);
}

// Summary
echo "=== SUMMARY ===\n";
$correctCount = 0;
$totalCount = count($testUrls);

foreach ($results as $url => $hasNoIndex) {
    $shouldHaveNoIndex = (
        strpos($url, '/testimonials/') !== false ||
        strpos($url, '/testimonial/') !== false ||
        strpos($url, '/subscriptions') !== false
    );
    
    $isCorrect = ($hasNoIndex === $shouldHaveNoIndex);
    if ($isCorrect) {
        $correctCount++;
    }
    
    $status = $isCorrect ? '✅' : '❌';
    $expected = $shouldHaveNoIndex ? 'noindex' : 'indexable';
    $actual = $hasNoIndex ? 'noindex' : 'indexable';
    
    echo "$status " . basename($url) . " (expected: $expected, actual: $actual)\n";
}

echo "\nResults: $correctCount/$totalCount URLs configured correctly\n";

if ($correctCount === $totalCount) {
    echo "🎉 All URLs are correctly configured!\n";
} else {
    echo "⚠️  Some URLs need attention. Check the implementation.\n";
}

// Implementation notes
echo "\n=== IMPLEMENTATION NOTES ===\n";
echo "✅ Added \$noIndex property to BaseController\n";
echo "✅ Updated beforeRender() to pass \$noIndex to views\n";
echo "✅ Modified meta_tags.ctp to output noindex when \$noIndex is true\n";
echo "✅ Set \$this->noIndex = true in TestimonialsController->view()\n";
echo "✅ Set \$this->noIndex = true in SubscriptionsController->index() and confirm()\n";
echo "\nThe implementation uses 'noindex, follow' which allows following links but prevents indexing.\n";
?>
