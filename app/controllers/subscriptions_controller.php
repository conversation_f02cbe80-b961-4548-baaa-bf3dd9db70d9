<?php

require(APP.'Recaptcha.php');


class SubscriptionsController extends AppController {

    var $name = 'Subscriptions';

    public $criticalCss = 'contact';

    function beforeFilter() {
        parent::beforeFilter();
        if ($this->Auth->allowedActions <> array('*')) {
            $this->Auth->allowedActions = array_merge(
                $this->Auth->allowedActions,
                array('confirm', 'test')
            );
        }

        $this->Security->disabledFields = array('Subscription.token');
    }

    function index() {
        $confirmed = false;

        // Set noindex for subscriptions page
        $this->noIndex = true;

        $this->bodyClass = $this->Subscription->bodyClass;

        $navigation = $this->Subscription->navigation;

        $breadcrumbs = array(array(
            'text' => 'Subscriptions',
            'url'  => $this->here
        ));

        if (!empty($this->data)) {

            if (!$this->isLocal()) {
            $recaptchaErrors = (new Recaptcha)->errors($this->data['Subscription']['token'], 'contact_form');

            if ($recaptchaErrors) {
                return $this->Session->setFlash('There is a problem with some of the detail entered: ' . $recaptchaErrors[0]);
            }
            }

            unset($this->data['Subscription']['token']);

            $this->Subscription->set($this->data);

            if($this->Subscription->validates()) {
                $code = $this->_subscribetest(
                    $this->data['Subscription']['email'],
                    $this->data['Subscription']['first_name'],
                    $this->data['Subscription']['last_name']
                );

                if ($code == 200) {
                    $confirmed = true;
                } else {
                    $this->Session->setFlash('There is a problem with some of the detail entered. Please correct the errors below.');
                }


                if ($code == 400) {
                    $this->Subscription->invalidate('email', 'Please enter a valid email address');
                } else if ($code == 202) {
                    $this->Subscription->invalidate('email', 'This email address has already subscribed');
                }
            } else {
                $this->Session->setFlash('There is a problem with some of the detail entered. Please correct the errors below.');
            }
        }

        $this->set(compact('navigation', 'breadcrumbs', 'confirmed'));
    }

    function confirm() {
        $params = $this->params['url'];
        $confirmed = false;

        // Set noindex for subscription confirmation page
        $this->noIndex = true;

        // Validate via confirmation link
        if (!empty($params['m']) && !empty($params['u'])) {
            $response = $this->_validate($params);

            $confirmed = true;
        }

        // Validate via PIN entry
        if (!empty($this->data)) {
            $this->Subscription->set($this->data);

            if($this->Subscription->validates()) {

                $response = $this->_validate(array(
                    'email' => $this->data['Subscription']['email'],
                    'pin' => $this->data['Subscription']['pin'],
                ));

                if ($response) {
                    $confirmed = true;
                } else {
                    $this->Subscription->invalidate('pin', 'Please enter a valid 4 digit pin');
                }

            } else {
                $this->Session->setFlash('There is a problem with some of the detail entered. Please correct the errors below.');
            }
        }

        $this->bodyClass = $this->Subscription->bodyClass;

        $navigation = $this->Subscription->navigation;

        $email = !empty($params['email']) ? $params['email'] : false;

        $breadcrumbs = array(array(
            'text' => 'Subscriptions',
            'url'  => $this->here
        ));

        $this->set(compact('navigation', 'breadcrumbs', 'email', 'confirmed', 'confirmed'));
    }

    function _validate($data) {
        App::import('Core', 'HttpSocket');
        $Http = new HttpSocket();
        $Http->post('https://email.bon-voyage.co.uk/validate.aspx', $data);
        return $Http->response['status']['code'] === 200;
    }

    protected function _subscribetest($email, $firstName, $lastName) {
        App::import('Core', 'HttpSocket');
        $Http = new HttpSocket();
        $data = array(
            'email'      => $email,
            'first_name' => $firstName,
            'last_name'  => $lastName,
            'url'        => 'https://www.bon-voyage.co.uk',
            'client_ip'  => isset($_SERVER['HTTP_X_FORWARDED_FOR']) ? $_SERVER['HTTP_X_FORWARDED_FOR'] : $_SERVER['REMOTE_ADDR'],
            'GCLID' => isset($_GET['gclid']) ? $_GET['gclid'] : '',
            'UTM' => $this->data['Contact']['utm'],
        );

        $Http->post('https://email.bon-voyage.co.uk/subscribe2.aspx', $data);
        return $Http->response['status']['code'];
    }

    private function isLocal()
    {
        return strpos($_SERVER['HTTP_HOST'],"localhost") !== false || strpos($_SERVER['HTTP_HOST'],"stage") !== false || strpos($_SERVER['HTTP_HOST'],"ddev") !== false;
    }


}
